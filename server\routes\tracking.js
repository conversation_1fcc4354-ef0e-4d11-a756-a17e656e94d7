/**
 * Tracking Routes - Ad Blocker Awareness and Session Management
 * Handles database operations for localStorage replacement
 */

const express = require('express');
const router = express.Router();
const { AdBlockerTrackingService } = require('../services/storageService');

// Middleware to get client info
const getClientInfo = (req) => ({
  userAgent: req.get('User-Agent'),
  ipAddress: req.ip || req.connection.remoteAddress
});

// Middleware to ensure session ID
const ensureSessionId = (req, res, next) => {
  if (!req.session.id) {
    req.session.id = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  next();
};

/**
 * Get ad blocker tracking record
 * GET /api/tracking/ad-blocker
 */
router.get('/ad-blocker', ensureSessionId, async (req, res) => {
  try {
    const sessionId = req.session.id;
    const record = await AdBlockerTrackingService.getTrackingRecord(sessionId);
    
    if (!record) {
      return res.json({
        success: true,
        data: null,
        message: 'No tracking record found'
      });
    }

    res.json({
      success: true,
      data: {
        session_id: record.session_id,
        user_id: record.user_id,
        last_shown_timestamp: record.last_shown_timestamp,
        dismiss_count: record.dismiss_count,
        user_agent: record.user_agent,
        ip_address: record.ip_address,
        created_at: record.created_at,
        updated_at: record.updated_at
      }
    });
  } catch (error) {
    console.error('Error getting ad blocker tracking record:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get tracking record'
    });
  }
});

/**
 * Save ad blocker tracking record
 * POST /api/tracking/ad-blocker
 */
router.post('/ad-blocker', ensureSessionId, async (req, res) => {
  try {
    const sessionId = req.session.id;
    const { lastShownTimestamp, dismissCount } = req.body;
    const clientInfo = getClientInfo(req);
    
    const data = {
      lastShownTimestamp: lastShownTimestamp || 0,
      dismissCount: dismissCount || 0
    };

    await AdBlockerTrackingService.saveTrackingRecord(
      sessionId,
      data,
      clientInfo.userAgent,
      clientInfo.ipAddress,
      req.user?.id // User ID if authenticated
    );

    res.json({
      success: true,
      message: 'Tracking record saved successfully'
    });
  } catch (error) {
    console.error('Error saving ad blocker tracking record:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to save tracking record'
    });
  }
});

/**
 * Record popup shown
 * POST /api/tracking/ad-blocker/shown
 */
router.post('/ad-blocker/shown', ensureSessionId, async (req, res) => {
  try {
    const sessionId = req.session.id;
    const clientInfo = getClientInfo(req);

    await AdBlockerTrackingService.recordPopupShown(
      sessionId,
      clientInfo.userAgent,
      clientInfo.ipAddress
    );

    res.json({
      success: true,
      message: 'Popup shown recorded successfully'
    });
  } catch (error) {
    console.error('Error recording popup shown:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to record popup shown'
    });
  }
});

/**
 * Record popup dismissed
 * POST /api/tracking/ad-blocker/dismissed
 */
router.post('/ad-blocker/dismissed', ensureSessionId, async (req, res) => {
  try {
    const sessionId = req.session.id;
    const clientInfo = getClientInfo(req);

    await AdBlockerTrackingService.recordPopupDismissed(
      sessionId,
      clientInfo.userAgent,
      clientInfo.ipAddress
    );

    res.json({
      success: true,
      message: 'Popup dismissed recorded successfully'
    });
  } catch (error) {
    console.error('Error recording popup dismissed:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to record popup dismissed'
    });
  }
});

/**
 * Delete tracking data
 * DELETE /api/tracking/ad-blocker
 */
router.delete('/ad-blocker', ensureSessionId, async (req, res) => {
  try {
    const sessionId = req.session.id;

    await AdBlockerTrackingService.resetTrackingData(sessionId);

    res.json({
      success: true,
      message: 'Tracking data reset successfully'
    });
  } catch (error) {
    console.error('Error resetting tracking data:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to reset tracking data'
    });
  }
});

/**
 * Record popup dismissed
 * POST /api/tracking/ad-blocker/dismissed
 */
router.post('/ad-blocker/dismissed', ensureSessionId, async (req, res) => {
  try {
    const sessionId = req.session.id;
    const clientInfo = getClientInfo(req);

    await AdBlockerTrackingService.recordPopupDismissed(
      sessionId,
      clientInfo.userAgent,
      clientInfo.ipAddress
    );

    res.json({
      success: true,
      message: 'Popup dismissed recorded successfully'
    });
  } catch (error) {
    console.error('Error recording popup dismissed:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to record popup dismissed'
    });
  }
});

/**
 * Reset tracking data
 * DELETE /api/tracking/ad-blocker
 */
router.delete('/ad-blocker', ensureSessionId, async (req, res) => {
  try {
    const sessionId = req.session.id;
    const success = await AdBlockerTrackingService.resetTrackingData(sessionId);

    if (success) {
      res.json({
        success: true,
        message: 'Tracking data reset successfully'
      });
    } else {
      res.status(404).json({
        success: false,
        error: 'No tracking data found to reset'
      });
    }
  } catch (error) {
    console.error('Error resetting tracking data:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to reset tracking data'
    });
  }
});

module.exports = router;
