#!/bin/bash

# StreamDB Online - Backend Server Security Hardening Script
# This script secures the backend server by closing unnecessary ports
# and configuring firewall rules for the two-tier proxy architecture

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROXY_SERVER_IP="*************"
BACKEND_SERVER_IP="***********"

# Function to log messages
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "This script must be run as root (use sudo)"
        exit 1
    fi
}

# Function to backup current UFW rules
backup_ufw_rules() {
    log_info "Backing up current UFW rules..."
    
    if command -v ufw >/dev/null 2>&1; then
        ufw status numbered > /tmp/ufw_backup_$(date +%Y%m%d_%H%M%S).txt
        log_success "UFW rules backed up to /tmp/"
    else
        log_warning "UFW not installed, installing..."
        apt-get update && apt-get install -y ufw
    fi
}

# Function to configure secure firewall rules
configure_firewall() {
    log_info "Configuring secure firewall rules..."
    
    # Reset UFW to clean state
    log_info "Resetting UFW to default configuration..."
    ufw --force reset
    
    # Set default policies
    ufw default deny incoming
    ufw default allow outgoing
    
    # Allow SSH (CRITICAL - don't lock out admin access)
    log_info "Allowing SSH access on port 22..."
    ufw allow 22/tcp
    
    # Allow ONLY proxy server to access web services
    log_info "Allowing proxy server (${PROXY_SERVER_IP}) to access web ports..."
    ufw allow from ${PROXY_SERVER_IP} to any port 80
    ufw allow from ${PROXY_SERVER_IP} to any port 443
    
    # Allow localhost connections (for database and internal services)
    log_info "Allowing localhost connections..."
    ufw allow from 127.0.0.1
    ufw allow from ::1
    
    # Enable firewall
    log_info "Enabling UFW firewall..."
    ufw --force enable
    
    log_success "Firewall configured successfully"
}

# Function to stop unnecessary services
stop_unnecessary_services() {
    log_info "Stopping unnecessary services..."
    
    # List of services to stop and disable
    services_to_stop=(
        "postfix"
        "dovecot" 
        "exim4"
        "sendmail"
        "courier-imap"
        "courier-pop"
    )
    
    for service in "${services_to_stop[@]}"; do
        if systemctl is-active --quiet "$service" 2>/dev/null; then
            log_info "Stopping $service..."
            systemctl stop "$service" || true
            systemctl disable "$service" || true
            log_success "$service stopped and disabled"
        else
            log_info "$service is not running or not installed"
        fi
    done
}

# Function to close specific ports
close_unnecessary_ports() {
    log_info "Removing any existing rules for unnecessary ports..."
    
    # Ports to explicitly deny/remove
    unnecessary_ports=(
        "7777"
        "8080"
        "3306"  # MySQL should only be localhost
        "8888"
        "3001"  # Node.js app should only be localhost
        "9000"  # Webhook port (removed)
        "25"    # SMTP
        "110"   # POP3
        "143"   # IMAP
        "993"   # IMAPS
        "995"   # POP3S
        "587"   # SMTP submission
    )
    
    for port in "${unnecessary_ports[@]}"; do
        # Try to delete any existing allow rules for these ports
        ufw delete allow "$port" 2>/dev/null || true
        ufw delete allow "$port/tcp" 2>/dev/null || true
        ufw delete allow "$port/udp" 2>/dev/null || true
        
        # Explicitly deny these ports from external access
        ufw deny "$port" 2>/dev/null || true
        
        log_info "Port $port access removed/denied"
    done
    
    log_success "Unnecessary ports closed"
}

# Function to verify security configuration
verify_security() {
    log_info "Verifying security configuration..."
    
    echo ""
    log_info "Current UFW status:"
    ufw status numbered
    
    echo ""
    log_info "Currently listening ports:"
    netstat -tlnp | grep LISTEN
    
    echo ""
    log_info "Active services:"
    systemctl list-units --type=service --state=active | grep -E "(apache|nginx|mysql|node|pm2)"
    
    log_success "Security verification complete"
}

# Function to test connectivity
test_connectivity() {
    log_info "Testing connectivity..."
    
    # Test localhost connections (should work)
    if curl -s --connect-timeout 5 http://127.0.0.1 >/dev/null 2>&1; then
        log_success "Localhost web access: OK"
    else
        log_warning "Localhost web access: Not responding (may be normal if no web server on port 80)"
    fi
    
    # Test application port locally (should work)
    if curl -s --connect-timeout 5 http://127.0.0.1:3001/api/health >/dev/null 2>&1; then
        log_success "Local application access: OK"
    else
        log_warning "Local application access: Not responding (check if app is running)"
    fi
    
    log_info "External access test requires testing from outside this server"
    log_info "From external machine, these should FAIL:"
    log_info "  curl --connect-timeout 10 http://${BACKEND_SERVER_IP}"
    log_info "  curl --connect-timeout 10 http://${BACKEND_SERVER_IP}:3001"
    log_info ""
    log_info "From proxy server (${PROXY_SERVER_IP}), these should WORK:"
    log_info "  curl -I http://${BACKEND_SERVER_IP}"
    log_info "  curl -I http://${BACKEND_SERVER_IP}:3001/api/health"
}

# Main execution
main() {
    log_info "🔒 StreamDB Backend Server Security Hardening"
    log_info "=============================================="
    
    # Check prerequisites
    check_root
    
    # Backup current configuration
    backup_ufw_rules
    
    # Configure security
    configure_firewall
    stop_unnecessary_services
    close_unnecessary_ports
    
    # Verify configuration
    verify_security
    test_connectivity
    
    echo ""
    log_success "🎉 Backend server security hardening completed!"
    log_info ""
    log_info "🔍 Next steps:"
    log_info "1. Test website access: https://streamdb.online"
    log_info "2. Test admin panel: https://streamdb.online/admin"
    log_info "3. Verify direct IP access is blocked from external networks"
    log_info "4. Monitor logs for any connectivity issues"
    log_info ""
    log_warning "⚠️  If you experience issues, check /tmp/ufw_backup_*.txt for rollback"
}

# Run main function
main "$@"
