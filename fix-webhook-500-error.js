#!/usr/bin/env node

/**
 * StreamDB Online - Fix Webhook 500 Error
 * 
 * This script fixes the GitHub webhook 500 error by ensuring all required
 * database tables and configurations are properly set up.
 */

const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const util = require('util');

const execAsync = util.promisify(exec);

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  bright: '\x1b[1m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️ ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️ ${message}`, 'blue');
}

async function runCommand(command, description) {
  try {
    logInfo(`Running: ${description}`);
    const { stdout, stderr } = await execAsync(command);
    
    if (stdout) {
      console.log(stdout);
    }
    
    if (stderr && !stderr.includes('warning')) {
      logWarning(`Warnings: ${stderr}`);
    }
    
    logSuccess(`${description} completed successfully`);
    return true;
  } catch (error) {
    logError(`${description} failed: ${error.message}`);
    if (error.stdout) {
      console.log('STDOUT:', error.stdout);
    }
    if (error.stderr) {
      console.log('STDERR:', error.stderr);
    }
    return false;
  }
}

async function checkServerDirectory() {
  const serverDir = path.join(__dirname, 'server');
  if (!fs.existsSync(serverDir)) {
    logError('Server directory not found');
    return false;
  }
  
  const envFile = path.join(serverDir, '.env');
  if (!fs.existsSync(envFile)) {
    logError('Server .env file not found');
    return false;
  }
  
  logSuccess('Server directory and .env file found');
  return true;
}

async function fixWebhook500Error() {
  log('🔧 StreamDB Online - Fix Webhook 500 Error', 'bright');
  log('============================================', 'bright');
  
  // Step 1: Check server directory structure
  log('\n📁 Step 1: Checking server directory...', 'cyan');
  const serverOk = await checkServerDirectory();
  if (!serverOk) {
    logError('Server directory check failed');
    return false;
  }
  
  // Step 2: Run webhook diagnostic
  log('\n🔍 Step 2: Running webhook diagnostic...', 'cyan');
  const diagnosticOk = await runCommand(
    'node server/scripts/webhook-diagnostic.js',
    'Webhook diagnostic'
  );
  
  // Step 3: Initialize deployment database
  log('\n🗄️ Step 3: Initializing deployment database...', 'cyan');
  const dbInitOk = await runCommand(
    'node server/scripts/init-deployment-db.js',
    'Deployment database initialization'
  );
  
  if (!dbInitOk) {
    logError('Database initialization failed');
    logInfo('This is likely the cause of the 500 error');
    return false;
  }
  
  // Step 4: Test database connection
  log('\n🧪 Step 4: Testing database connection...', 'cyan');
  const dbTestOk = await runCommand(
    'node server/test-db-connection.js',
    'Database connection test'
  );
  
  // Step 5: Run diagnostic again to verify fix
  log('\n✅ Step 5: Verifying fix...', 'cyan');
  const finalDiagnosticOk = await runCommand(
    'node server/scripts/webhook-diagnostic.js',
    'Final webhook diagnostic'
  );
  
  // Summary
  log('\n📊 Fix Summary', 'cyan');
  log('==============', 'cyan');
  
  if (dbInitOk && dbTestOk && finalDiagnosticOk) {
    logSuccess('Webhook 500 error should now be fixed!');
    log('\n🚀 Next Steps:', 'green');
    log('  1. Test the webhook endpoint: https://streamdb.online/api/webhook/test', 'green');
    log('  2. Try triggering the GitHub webhook again', 'green');
    log('  3. Check the admin panel deployment status', 'green');
    return true;
  } else {
    logError('Some steps failed. The 500 error may persist.');
    log('\n🔧 Manual Steps Required:', 'yellow');
    
    if (!dbInitOk) {
      log('  • Manually run: node server/scripts/init-deployment-db.js', 'yellow');
      log('  • Check database credentials in server/.env', 'yellow');
    }
    
    if (!dbTestOk) {
      log('  • Verify MySQL server is running', 'yellow');
      log('  • Check database permissions', 'yellow');
    }
    
    return false;
  }
}

// Run the fix if called directly
if (require.main === module) {
  fixWebhook500Error()
    .then(success => {
      if (success) {
        log('\n🎉 Webhook fix completed successfully!', 'green');
        process.exit(0);
      } else {
        log('\n❌ Webhook fix failed. Manual intervention required.', 'red');
        process.exit(1);
      }
    })
    .catch(error => {
      logError(`Fix script failed: ${error.message}`);
      process.exit(1);
    });
}

module.exports = { fixWebhook500Error };
