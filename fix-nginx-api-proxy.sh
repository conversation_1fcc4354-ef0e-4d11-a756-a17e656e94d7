#!/bin/bash

echo "=== Fixing Nginx API Proxy Configuration ==="

# Backup current config
echo "Creating backup..."
sudo cp /etc/nginx/fastpanel2-sites/streamdb_onl_usr/streamdb.online.conf /etc/nginx/fastpanel2-sites/streamdb_onl_usr/streamdb.online.conf.backup

# Fix the proxy_pass directive
echo "Fixing proxy_pass directive..."
sudo sed -i 's|proxy_pass http://127.0.0.1:3001/;|proxy_pass http://127.0.0.1:3001/api/;|' /etc/nginx/fastpanel2-sites/streamdb_onl_usr/streamdb.online.conf

# Show the change
echo "Configuration change made:"
grep -A 1 -B 1 "proxy_pass" /etc/nginx/fastpanel2-sites/streamdb_onl_usr/streamdb.online.conf

# Test nginx configuration
echo "Testing nginx configuration..."
sudo nginx -t

if [ $? -eq 0 ]; then
    echo "Configuration test passed! Reloading nginx..."
    sudo systemctl reload nginx
    
    echo "Testing API endpoint..."
    curl -k -X POST https://45.93.8.197/api/auth/login -H "Content-Type: application/json" -d '{"username":"test","password":"testpass"}'
    
    echo ""
    echo "=== Fix Complete! ==="
    echo "Your website should now be fully functional."
    echo "API requests will be properly proxied to your Node.js backend."
else
    echo "Configuration test failed! Restoring backup..."
    sudo cp /etc/nginx/fastpanel2-sites/streamdb_onl_usr/streamdb.online.conf.backup /etc/nginx/fastpanel2-sites/streamdb_onl_usr/streamdb.online.conf
fi
