#!/bin/bash

# Fix FastPanel Proxy Configuration
# This script configures the proxy server to properly route FastPanel traffic

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

# Function to configure FastPanel proxy on proxy server
configure_fastpanel_proxy() {
    log_info "🔧 Configuring FastPanel Proxy on Proxy Server"
    echo "================================================"
    
    # Create FastPanel Nginx configuration
    cat > /etc/nginx/sites-available/fastpanel.streamdb.online << 'EOF'
server {
    listen 80;
    server_name fastpanel.streamdb.online;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name fastpanel.streamdb.online;
    
    # SSL Configuration (using same cert as main site)
    ssl_certificate /etc/ssl/certs/streamdb.online.crt;
    ssl_private_key /etc/ssl/private/streamdb.online.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # Security headers
    add_header X-Frame-Options SAMEORIGIN;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy "no-referrer-when-downgrade";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # Proxy to backend FastPanel HTTPS service
    location / {
        proxy_pass https://***********:8888;
        proxy_ssl_verify off;
        proxy_ssl_server_name on;
        proxy_ssl_name fastpanel.streamdb.online;
        
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # Buffer settings
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        proxy_busy_buffers_size 8k;
    }
    
    # Handle static assets
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        proxy_pass https://***********:8888;
        proxy_ssl_verify off;
        proxy_set_header Host $host;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Error and access logs
    error_log /var/log/nginx/fastpanel.streamdb.online.error.log;
    access_log /var/log/nginx/fastpanel.streamdb.online.access.log;
}
EOF
    
    log_success "FastPanel Nginx configuration created"
}

# Function to enable the configuration
enable_fastpanel_config() {
    log_info "🔗 Enabling FastPanel Configuration"
    echo "==================================="
    
    # Enable the site
    ln -sf /etc/nginx/sites-available/fastpanel.streamdb.online /etc/nginx/sites-enabled/
    
    # Test Nginx configuration
    if nginx -t; then
        log_success "Nginx configuration test passed"
    else
        log_error "Nginx configuration test failed"
        return 1
    fi
    
    # Reload Nginx
    systemctl reload nginx
    log_success "Nginx reloaded successfully"
}

# Function to configure backend FastPanel
configure_backend_fastpanel() {
    log_info "🔧 Configuring Backend FastPanel"
    echo "================================="
    
    # SSH to backend and configure FastPanel
    ssh -o StrictHostKeyChecking=no root@*********** << 'BACKEND_EOF'
# Ensure FastPanel HTTPS is running on port 8888
if ! netstat -tlnp | grep -q ":8888"; then
    echo "❌ FastPanel HTTPS not running on port 8888"
    exit 1
fi

# Create FastPanel Nginx configuration for external access
cat > /etc/nginx/conf.d/fastpanel-external.conf << 'EOF'
server {
    listen 8888 ssl http2;
    server_name fastpanel.streamdb.online ***********;
    
    # SSL Configuration
    ssl_certificate /usr/local/fastpanel2/ssl/fastpanel.crt;
    ssl_private_key /usr/local/fastpanel2/ssl/fastpanel.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    
    # Root directory
    root /usr/local/fastpanel2/web/public;
    index index.html index.htm;
    
    # Security headers
    add_header X-Frame-Options SAMEORIGIN;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    # Main location
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # API proxy to FastPanel backend
    location /api/ {
        proxy_pass http://127.0.0.1:5501;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Static assets
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
EOF

# Test and reload Nginx
nginx -t && systemctl reload nginx

echo "✅ Backend FastPanel configured"
BACKEND_EOF
    
    log_success "Backend FastPanel configured"
}

# Function to test the complete setup
test_fastpanel_access() {
    log_info "🧪 Testing FastPanel Access"
    echo "============================"
    
    # Test from proxy server
    log_info "Testing from proxy server:"
    
    # Test HTTP redirect
    local http_response=$(curl -s -o /dev/null -w "%{http_code}" http://fastpanel.streamdb.online/ 2>/dev/null)
    log_info "HTTP redirect test: $http_response"
    
    # Test HTTPS access
    local https_response=$(curl -s -o /dev/null -w "%{http_code}" https://fastpanel.streamdb.online/ 2>/dev/null)
    log_info "HTTPS access test: $https_response"
    
    # Test backend direct access
    local backend_response=$(curl -k -s -o /dev/null -w "%{http_code}" https://***********:8888/ 2>/dev/null)
    log_info "Backend direct access: $backend_response"
    
    if [ "$https_response" = "200" ]; then
        log_success "FastPanel is accessible via HTTPS"
    else
        log_warning "FastPanel HTTPS access returned: $https_response"
    fi
}

# Function to create monitoring script
create_fastpanel_monitor() {
    log_info "📊 Creating FastPanel Monitor"
    echo "=============================="
    
    cat > /usr/local/bin/fastpanel-monitor.sh << 'EOF'
#!/bin/bash

# FastPanel Monitoring Script

echo "=== FastPanel Status Monitor ==="
echo "Date: $(date)"
echo ""

# Check proxy server Nginx
echo "Proxy Server Nginx:"
if systemctl is-active --quiet nginx; then
    echo "✅ Nginx: Running"
else
    echo "❌ Nginx: Not Running"
fi

# Check FastPanel site configuration
echo "FastPanel Site Config:"
if [ -f "/etc/nginx/sites-enabled/fastpanel.streamdb.online" ]; then
    echo "✅ FastPanel config: Enabled"
else
    echo "❌ FastPanel config: Missing"
fi

# Test FastPanel access
echo ""
echo "Access Tests:"
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" https://fastpanel.streamdb.online/ 2>/dev/null)
echo "FastPanel HTTPS: $HTTP_CODE"

BACKEND_CODE=$(curl -k -s -o /dev/null -w "%{http_code}" https://***********:8888/ 2>/dev/null)
echo "Backend Direct: $BACKEND_CODE"

echo ""
echo "=== Monitor Complete ==="
EOF
    
    chmod +x /usr/local/bin/fastpanel-monitor.sh
    log_success "FastPanel monitor created: /usr/local/bin/fastpanel-monitor.sh"
}

# Main execution
main() {
    log_info "🔧 FastPanel Proxy Fix"
    log_info "======================"
    
    # Check if we're on the proxy server
    local current_ip=$(hostname -I | awk '{print $1}')
    if [ "$current_ip" != "*************" ]; then
        log_error "This script must be run on the proxy server (*************)"
        log_info "Current IP: $current_ip"
        exit 1
    fi
    
    configure_fastpanel_proxy
    echo ""
    enable_fastpanel_config
    echo ""
    configure_backend_fastpanel
    echo ""
    test_fastpanel_access
    echo ""
    create_fastpanel_monitor
    
    echo ""
    log_success "🎉 FastPanel Proxy Fix Completed!"
    log_info ""
    log_info "📋 Summary:"
    log_info "   ✅ Proxy server configured for fastpanel.streamdb.online"
    log_info "   ✅ Backend FastPanel configured for external access"
    log_info "   ✅ HTTPS routing properly configured"
    log_info "   ✅ Monitoring script created"
    
    echo ""
    log_info "🌐 FastPanel should now be accessible at:"
    log_info "   https://fastpanel.streamdb.online/"
    
    echo ""
    log_info "🔧 Monitor FastPanel status with:"
    log_info "   /usr/local/bin/fastpanel-monitor.sh"
}

# Run main function
main "$@"
