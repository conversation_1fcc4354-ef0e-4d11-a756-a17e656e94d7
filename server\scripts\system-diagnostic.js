#!/usr/bin/env node

/**
 * StreamDB System Diagnostic Tool
 * Comprehensive system audit and troubleshooting for production environment
 */

const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');
const execAsync = promisify(exec);

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '../.env') });

const db = require('../config/database');

class SystemDiagnostic {
  constructor() {
    this.results = {
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      tests: [],
      summary: {
        passed: 0,
        failed: 0,
        warnings: 0
      }
    };
  }

  log(level, test, message, details = {}) {
    const result = {
      level,
      test,
      message,
      details,
      timestamp: new Date().toISOString()
    };

    this.results.tests.push(result);
    this.results.summary[level === 'PASS' ? 'passed' : level === 'FAIL' ? 'failed' : 'warnings']++;

    const colors = {
      PASS: '\x1b[32m✅',
      FAIL: '\x1b[31m❌',
      WARN: '\x1b[33m⚠️',
      INFO: '\x1b[34mℹ️'
    };

    console.log(`${colors[level] || colors.INFO} ${test}: ${message}\x1b[0m`);
    if (Object.keys(details).length > 0) {
      console.log(`   Details:`, details);
    }
  }

  async testDatabaseConnection() {
    try {
      // Test 1: Check if database module is available
      if (!db || !db.query) {
        this.log('FAIL', 'Database Module', 'Database module not available', {
          dbObject: !!db,
          queryMethod: !!(db && db.query),
          recommendation: 'Check database configuration and imports'
        });
        return false;
      }

      // Test 2: Basic connectivity test
      await new Promise((resolve, reject) => {
        db.query('SELECT 1 as test', (err, results) => {
          if (err) reject(err);
          else resolve(results);
        });
      });

      this.log('PASS', 'Database Connection', 'Successfully connected to MySQL database');

      // Test 3: Check deployment tables exist
      const requiredTables = ['deployments', 'deployment_logs', 'webhook_events', 'manual_deployments', 'deployment_config'];
      const existingTables = [];

      for (const table of requiredTables) {
        try {
          await new Promise((resolve, reject) => {
            db.query(`SHOW TABLES LIKE '${table}'`, (err, results) => {
              if (err) reject(err);
              else resolve(results);
            });
          });
          existingTables.push(table);
        } catch (error) {
          // Table doesn't exist or error accessing it
        }
      }

      if (existingTables.length === requiredTables.length) {
        this.log('PASS', 'Database Tables', 'All deployment tables exist', {
          tables: existingTables
        });
      } else {
        const missingTables = requiredTables.filter(t => !existingTables.includes(t));
        this.log('WARN', 'Database Tables', 'Some deployment tables missing', {
          existing: existingTables,
          missing: missingTables,
          recommendation: 'Run: node server/scripts/simple-db-init.js'
        });
      }

      // Test 4: Test database write permissions
      try {
        await new Promise((resolve, reject) => {
          db.query(
            'INSERT INTO deployment_logs (level, message, data) VALUES (?, ?, ?)',
            ['INFO', 'System diagnostic test', JSON.stringify({
              test: true,
              timestamp: new Date().toISOString()
            })],
            (err, result) => {
              if (err) reject(err);
              else resolve(result);
            }
          );
        });
        this.log('PASS', 'Database Write', 'Database write permissions working');
      } catch (error) {
        this.log('WARN', 'Database Write', 'Database write test failed', {
          error: error.message,
          recommendation: 'Check if deployment tables exist and user has INSERT permissions'
        });
      }

      // Test 5: Check database configuration
      const dbConfig = {
        host: process.env.DB_HOST,
        port: process.env.DB_PORT,
        database: process.env.DB_NAME,
        user: process.env.DB_USER,
        socket: process.env.DB_SOCKET
      };

      this.log('PASS', 'Database Config', 'Database configuration validated', {
        connectionType: dbConfig.socket ? 'socket' : 'tcp',
        host: dbConfig.host,
        port: dbConfig.port,
        database: dbConfig.database,
        socket: dbConfig.socket
      });

      return true;
    } catch (error) {
      this.log('FAIL', 'Database Connection', 'Failed to connect to database', {
        error: error.message,
        host: process.env.DB_HOST,
        port: process.env.DB_PORT,
        database: process.env.DB_NAME,
        socket: process.env.DB_SOCKET
      });
      return false;
    }
  }

  async testWebhookConfiguration() {
    // Test 1: Webhook Secret Configuration
    const webhookSecret = process.env.WEBHOOK_SECRET;
    if (!webhookSecret) {
      this.log('FAIL', 'Webhook Secret', 'WEBHOOK_SECRET not configured', {
        recommendation: 'Set WEBHOOK_SECRET in .env file'
      });
      return false;
    }

    if (webhookSecret.length < 32) {
      this.log('WARN', 'Webhook Secret', 'WEBHOOK_SECRET should be at least 32 characters', {
        currentLength: webhookSecret.length,
        recommendation: 'Generate a stronger secret: openssl rand -hex 32'
      });
    } else {
      this.log('PASS', 'Webhook Secret', 'WEBHOOK_SECRET properly configured', {
        length: webhookSecret.length
      });
    }

    // Test 2: GitHub Repository Configuration
    const githubRepo = process.env.GITHUB_REPO;
    if (!githubRepo) {
      this.log('WARN', 'GitHub Repository', 'GITHUB_REPO not configured');
    } else {
      this.log('PASS', 'GitHub Repository', 'GitHub repository configured', {
        repository: githubRepo
      });
    }

    // Test 3: Deployment Script
    const deployScript = process.env.DEPLOY_SCRIPT;
    if (!deployScript || !fs.existsSync(deployScript)) {
      this.log('FAIL', 'Deployment Script', 'Deploy script not found', {
        path: deployScript,
        recommendation: 'Check DEPLOY_SCRIPT path in .env file'
      });
      return false;
    }

    try {
      const stats = fs.statSync(deployScript);
      if (!(stats.mode & parseInt('100', 8))) {
        this.log('WARN', 'Deployment Script', 'Script not executable, attempting to fix');
        fs.chmodSync(deployScript, '755');
        this.log('PASS', 'Deployment Script', 'Script made executable');
      } else {
        this.log('PASS', 'Deployment Script', 'Deploy script exists and is executable');
      }

      // Test script syntax
      try {
        await execAsync(`bash -n "${deployScript}"`);
        this.log('PASS', 'Script Syntax', 'Deployment script syntax is valid');
      } catch (error) {
        this.log('FAIL', 'Script Syntax', 'Deployment script has syntax errors', {
          error: error.message
        });
        return false;
      }
    } catch (error) {
      this.log('FAIL', 'Deployment Script', 'Cannot access deploy script', { error: error.message });
      return false;
    }

    // Test 4: Webhook Branch Configuration
    const deployBranch = process.env.DEPLOY_BRANCH || 'New-Main-1';
    this.log('PASS', 'Deploy Branch', 'Deployment branch configured', {
      branch: deployBranch
    });

    // Test 5: Webhook Port Configuration
    const webhookPort = process.env.WEBHOOK_PORT || 9000;
    this.log('PASS', 'Webhook Port', 'Webhook port configured', {
      port: webhookPort,
      note: 'Ensure this port is accessible from GitHub'
    });

    return true;
  }

  async testGitRepository() {
    try {
      const projectDir = path.join(__dirname, '../..');
      const { stdout: branch } = await execAsync('git branch --show-current', { cwd: projectDir });
      const { stdout: status } = await execAsync('git status --porcelain', { cwd: projectDir });
      const { stdout: lastCommit } = await execAsync('git log -1 --oneline', { cwd: projectDir });

      this.log('PASS', 'Git Repository', 'Git repository accessible', {
        currentBranch: branch.trim(),
        hasUncommittedChanges: status.trim().length > 0,
        lastCommit: lastCommit.trim()
      });

      // Test GitHub connectivity
      await execAsync('git fetch --dry-run origin', { cwd: projectDir, timeout: 10000 });
      this.log('PASS', 'GitHub Connectivity', 'Can connect to GitHub repository');

      return true;
    } catch (error) {
      this.log('FAIL', 'Git Repository', 'Git repository issues', { error: error.message });
      return false;
    }
  }

  async testPM2Processes() {
    try {
      const { stdout } = await execAsync('pm2 jlist');
      const processes = JSON.parse(stdout);
      
      const streamdbProcess = processes.find(p => p.name === 'streamdb-online' || p.name === 'index');
      const webhookProcess = processes.find(p => p.name === 'webhook-server' || p.name === 'github-webhook');

      if (streamdbProcess) {
        this.log('PASS', 'PM2 Main Process', `Main application running (${streamdbProcess.pm2_env.status})`, {
          pid: streamdbProcess.pid,
          uptime: streamdbProcess.pm2_env.pm_uptime,
          restarts: streamdbProcess.pm2_env.restart_time
        });
      } else {
        this.log('FAIL', 'PM2 Main Process', 'Main application not running in PM2');
      }

      if (webhookProcess) {
        this.log('PASS', 'PM2 Webhook Process', `Webhook server running (${webhookProcess.pm2_env.status})`, {
          pid: webhookProcess.pid,
          port: webhookProcess.pm2_env.WEBHOOK_PORT || 9000
        });
      } else {
        this.log('WARN', 'PM2 Webhook Process', 'Webhook server not running as separate PM2 process');
      }

      return true;
    } catch (error) {
      this.log('FAIL', 'PM2 Processes', 'Cannot check PM2 processes', { error: error.message });
      return false;
    }
  }

  async testNginxConfiguration() {
    try {
      // Test if nginx is running
      await execAsync('systemctl is-active nginx');
      this.log('PASS', 'Nginx Service', 'Nginx is running');

      // Test nginx configuration
      await execAsync('nginx -t');
      this.log('PASS', 'Nginx Configuration', 'Nginx configuration is valid');

      return true;
    } catch (error) {
      this.log('FAIL', 'Nginx', 'Nginx issues detected', { error: error.message });
      return false;
    }
  }

  async testAPIEndpoints() {
    const endpoints = [
      '/api/health',
      '/api/webhook/health'
    ];

    for (const endpoint of endpoints) {
      try {
        const url = `http://localhost:${process.env.PORT || 3001}${endpoint}`;
        const response = await fetch(url);

        if (response.ok) {
          this.log('PASS', 'API Endpoint', `${endpoint} responding correctly`);
        } else {
          this.log('FAIL', 'API Endpoint', `${endpoint} returned ${response.status}`);
        }
      } catch (error) {
        this.log('FAIL', 'API Endpoint', `${endpoint} not accessible`, { error: error.message });
      }
    }
  }

  async testReverseProxySetup() {
    try {
      const expectedProxyIP = '*************';
      const backendServerIP = '***********';

      this.log('INFO', 'Reverse Proxy Architecture', 'Testing two-tier offshore VPS setup', {
        expectedFlow: 'Client → Cloudflare → ************* (Proxy) → *********** (Backend)',
        proxyServerIP: expectedProxyIP,
        backendServerIP: backendServerIP
      });

      // Test 1: Check if application is configured for proxy environment
      const hasHTTPS = process.env.FRONTEND_URL?.includes('https://');
      const hasTrustProxy = process.env.TRUST_PROXY || 'false';

      if (hasHTTPS) {
        this.log('PASS', 'Proxy Configuration', 'Application configured for HTTPS proxy environment');
      } else {
        this.log('WARN', 'Proxy Configuration', 'Application may not be configured for HTTPS');
      }

      // Test 2: Check trust proxy setting
      if (hasTrustProxy === 'true' || process.env.NODE_ENV === 'production') {
        this.log('PASS', 'Trust Proxy', 'Application configured to trust proxy headers');
      } else {
        this.log('WARN', 'Trust Proxy', 'Application may not trust proxy headers properly');
      }

      // Test 3: Test external connectivity to verify proxy hiding
      try {
        const { stdout: publicIP } = await execAsync('curl -s -m 5 https://ipinfo.io/ip || curl -s -m 5 https://api.ipify.org || echo "unknown"');
        const detectedIP = publicIP.trim();

        if (detectedIP === backendServerIP) {
          this.log('FAIL', 'IP Hiding', 'Backend server IP is exposed to public', {
            detectedIP: detectedIP,
            expectedHidden: backendServerIP,
            recommendation: 'Configure firewall to block direct access to backend server'
          });
        } else if (detectedIP === expectedProxyIP) {
          this.log('PASS', 'IP Hiding', 'Proxy server IP detected (good)', {
            detectedIP: detectedIP,
            proxyIP: expectedProxyIP
          });
        } else {
          this.log('INFO', 'IP Hiding', 'External IP detection result', {
            detectedIP: detectedIP,
            note: 'Manual verification recommended'
          });
        }
      } catch (error) {
        this.log('WARN', 'IP Detection', 'Could not detect external IP', { error: error.message });
      }

      // Test 4: Check open ports that might expose backend
      try {
        const { stdout: netstatOutput } = await execAsync('netstat -tlnp | grep LISTEN');
        const openPorts = netstatOutput.split('\n')
          .filter(line => line.includes('0.0.0.0:') || line.includes(':::'))
          .map(line => {
            const match = line.match(/:(\d+)\s/);
            return match ? match[1] : null;
          })
          .filter(port => port !== null);

        const exposedPorts = openPorts.filter(port =>
          !['22', '80', '443'].includes(port) // Standard ports that should be open
        );

        if (exposedPorts.length > 0) {
          this.log('WARN', 'Port Security', 'Additional ports exposed to public', {
            exposedPorts: exposedPorts,
            recommendation: 'Consider restricting these ports: ' + exposedPorts.join(', '),
            securityRisk: 'These ports may compromise reverse proxy IP hiding'
          });
        } else {
          this.log('PASS', 'Port Security', 'Only standard ports (22, 80, 443) exposed');
        }
      } catch (error) {
        this.log('WARN', 'Port Check', 'Could not check open ports', { error: error.message });
      }

      // Test 5: Validate proxy headers in application
      const expressApp = process.env.TRUST_PROXY;
      if (expressApp) {
        this.log('PASS', 'Express Trust Proxy', 'Express.js configured to trust proxy');
      } else {
        this.log('WARN', 'Express Trust Proxy', 'Express.js trust proxy not explicitly configured');
      }

      return true;
    } catch (error) {
      this.log('FAIL', 'Reverse Proxy', 'Proxy configuration test failed', { error: error.message });
      return false;
    }
  }

  async testSecurityConfiguration() {
    this.log('INFO', 'Security Audit', 'Starting comprehensive security check');

    // Test 1: Environment Variables Security
    const sensitiveVars = ['DB_PASSWORD', 'JWT_SECRET', 'SESSION_SECRET', 'WEBHOOK_SECRET'];
    for (const varName of sensitiveVars) {
      if (process.env[varName]) {
        this.log('PASS', 'Sensitive Variables', `${varName} is configured`);
      } else {
        this.log('FAIL', 'Sensitive Variables', `${varName} is missing`, {
          recommendation: `Set ${varName} in .env file`
        });
      }
    }

    // Test 2: File Permissions
    const criticalFiles = [
      { path: path.join(__dirname, '../.env'), name: '.env file' },
      { path: process.env.DEPLOY_SCRIPT, name: 'deployment script' }
    ];

    for (const file of criticalFiles) {
      if (file.path && fs.existsSync(file.path)) {
        try {
          const stats = fs.statSync(file.path);
          const permissions = (stats.mode & parseInt('777', 8)).toString(8);

          if (file.name === '.env file' && permissions !== '600') {
            this.log('WARN', 'File Permissions', `${file.name} permissions too open`, {
              current: permissions,
              recommended: '600',
              command: `chmod 600 ${file.path}`
            });
          } else {
            this.log('PASS', 'File Permissions', `${file.name} permissions OK`, {
              permissions: permissions
            });
          }
        } catch (error) {
          this.log('WARN', 'File Permissions', `Cannot check ${file.name} permissions`, {
            error: error.message
          });
        }
      }
    }

    // Test 3: Network Security
    try {
      const { stdout } = await execAsync('ss -tlnp | grep LISTEN || netstat -tlnp | grep LISTEN');
      const listeningPorts = stdout.split('\n')
        .filter(line => line.includes(':'))
        .map(line => {
          const match = line.match(/:(\d+)\s/);
          return match ? match[1] : null;
        })
        .filter(port => port !== null);

      const publicPorts = listeningPorts.filter(port =>
        stdout.includes(`0.0.0.0:${port}`) || stdout.includes(`:::${port}`)
      );

      const unexpectedPorts = publicPorts.filter(port =>
        !['22', '80', '443', '3001', '9000'].includes(port)
      );

      if (unexpectedPorts.length > 0) {
        this.log('WARN', 'Network Security', 'Unexpected ports open to public', {
          unexpectedPorts: unexpectedPorts,
          allPublicPorts: publicPorts,
          recommendation: 'Review firewall rules and close unnecessary ports'
        });
      } else {
        this.log('PASS', 'Network Security', 'Port configuration looks secure', {
          publicPorts: publicPorts
        });
      }
    } catch (error) {
      this.log('WARN', 'Network Security', 'Could not check network ports', {
        error: error.message
      });
    }

    // Test 4: SSL/TLS Configuration
    const frontendUrl = process.env.FRONTEND_URL;
    if (frontendUrl && frontendUrl.startsWith('https://')) {
      this.log('PASS', 'SSL Configuration', 'HTTPS configured for frontend');
    } else {
      this.log('WARN', 'SSL Configuration', 'HTTPS not configured', {
        currentUrl: frontendUrl,
        recommendation: 'Configure SSL certificate for production'
      });
    }

    return true;
  }

  async runAllTests() {
    console.log('\n🔍 StreamDB System Diagnostic Starting...\n');
    console.log('🏗️ Infrastructure: Two-tier offshore VPS reverse proxy');
    console.log('🌐 Flow: Client → Cloudflare → ************* → ***********');
    console.log('=' .repeat(70));

    await this.testDatabaseConnection();
    await this.testWebhookConfiguration();
    await this.testGitRepository();
    await this.testPM2Processes();
    await this.testNginxConfiguration();
    await this.testAPIEndpoints();
    await this.testReverseProxySetup();
    await this.testSecurityConfiguration();

    console.log('\n📊 Diagnostic Summary:');
    console.log('=' .repeat(50));
    console.log(`✅ Passed: ${this.results.summary.passed}`);
    console.log(`❌ Failed: ${this.results.summary.failed}`);
    console.log(`⚠️  Warnings: ${this.results.summary.warnings}`);

    // Generate recommendations
    const failedTests = this.results.tests.filter(t => t.level === 'FAIL');
    const warningTests = this.results.tests.filter(t => t.level === 'WARN');

    if (failedTests.length > 0) {
      console.log('\n🚨 Critical Issues Requiring Immediate Attention:');
      failedTests.forEach(test => {
        console.log(`❌ ${test.test}: ${test.message}`);
        if (test.details.recommendation) {
          console.log(`   💡 Fix: ${test.details.recommendation}`);
        }
      });
    }

    if (warningTests.length > 0) {
      console.log('\n⚠️  Warnings (Recommended Improvements):');
      warningTests.forEach(test => {
        console.log(`⚠️  ${test.test}: ${test.message}`);
        if (test.details.recommendation) {
          console.log(`   💡 Suggestion: ${test.details.recommendation}`);
        }
      });
    }

    // Save results to file
    const resultsFile = path.join(__dirname, '../logs/diagnostic-results.json');
    try {
      fs.mkdirSync(path.dirname(resultsFile), { recursive: true });
      fs.writeFileSync(resultsFile, JSON.stringify(this.results, null, 2));
      console.log(`\n📄 Full results saved to: ${resultsFile}`);
    } catch (error) {
      console.log(`\n⚠️  Could not save results file: ${error.message}`);
    }

    // Overall health score
    const totalTests = this.results.tests.length;
    const healthScore = Math.round((this.results.summary.passed / totalTests) * 100);

    console.log(`\n🎯 Overall System Health: ${healthScore}%`);
    if (healthScore >= 90) {
      console.log('🟢 Excellent - System is in great condition');
    } else if (healthScore >= 75) {
      console.log('🟡 Good - Minor issues to address');
    } else if (healthScore >= 60) {
      console.log('🟠 Fair - Several issues need attention');
    } else {
      console.log('🔴 Poor - Critical issues require immediate action');
    }

    return this.results;
  }
}

// Run diagnostic if called directly
if (require.main === module) {
  const diagnostic = new SystemDiagnostic();
  diagnostic.runAllTests().catch(console.error);
}

module.exports = SystemDiagnostic;
