#!/usr/bin/env node

/**
 * StreamDB Online - Production Issues Fix Script
 * Comprehensive fix for all identified production issues
 */

const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const util = require('util');
const execAsync = util.promisify(exec);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSection(title) {
  console.log('\n' + '='.repeat(60));
  log(title, 'cyan');
  console.log('='.repeat(60));
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

class ProductionFixer {
  constructor() {
    this.fixes = [];
    this.errors = [];
  }

  async fixEnvironmentConfiguration() {
    logSection('🔧 Fixing Environment Configuration');
    
    const envPath = path.join(__dirname, '.env');
    
    if (!fs.existsSync(envPath)) {
      logError('Environment file not found');
      return false;
    }

    try {
      let envContent = fs.readFileSync(envPath, 'utf8');
      let modified = false;

      // Fix NODE_ENV
      if (envContent.includes('NODE_ENV=development')) {
        envContent = envContent.replace('NODE_ENV=development', 'NODE_ENV=production');
        modified = true;
        logSuccess('Fixed NODE_ENV to production');
      }

      // Ensure socket path is properly configured for production
      if (!envContent.includes('DB_SOCKET=/var/run/mysqld/mysqld.sock')) {
        // Add or uncomment socket path
        if (envContent.includes('# DB_SOCKET=')) {
          envContent = envContent.replace('# DB_SOCKET=/var/run/mysqld/mysqld.sock', 'DB_SOCKET=/var/run/mysqld/mysqld.sock');
        } else if (!envContent.includes('DB_SOCKET=')) {
          envContent = envContent.replace('DB_PORT=3306', 'DB_PORT=3306\nDB_SOCKET=/var/run/mysqld/mysqld.sock');
        }
        modified = true;
        logSuccess('Fixed database socket configuration');
      }

      if (modified) {
        fs.writeFileSync(envPath, envContent);
        logSuccess('Environment file updated successfully');
      } else {
        logInfo('Environment file already correctly configured');
      }

      return true;
    } catch (error) {
      logError(`Failed to fix environment: ${error.message}`);
      return false;
    }
  }

  async createNecessaryDirectories() {
    logSection('📁 Creating Necessary Directories');
    
    const directories = [
      path.join(__dirname, 'logs'),
      path.join(__dirname, 'uploads', 'images'),
      path.join(__dirname, 'uploads', 'videos'),
      path.join(__dirname, 'uploads', 'subtitles'),
      path.join(__dirname, 'uploads', 'temp')
    ];

    for (const dir of directories) {
      try {
        if (!fs.existsSync(dir)) {
          fs.mkdirSync(dir, { recursive: true });
          logSuccess(`Created directory: ${dir}`);
        } else {
          logInfo(`Directory already exists: ${dir}`);
        }
      } catch (error) {
        logError(`Failed to create directory ${dir}: ${error.message}`);
      }
    }
  }

  async generateProductionStartScript() {
    logSection('🚀 Generating Production Start Script');
    
    const startScript = `#!/bin/bash

# StreamDB Online - Production Start Script
# This script properly starts the application on the production server

set -e

echo "🚀 Starting StreamDB Online Production Server..."

# Navigate to project directory
cd /var/www/streamdb_onl_usr/data/www/streamdb.online/server

# Load environment variables
if [ ! -f .env ]; then
    echo "❌ Environment file not found!"
    exit 1
fi

# Check if PM2 is installed
if ! command -v pm2 &> /dev/null; then
    echo "❌ PM2 is not installed!"
    echo "Installing PM2..."
    npm install -g pm2
fi

# Stop existing processes
echo "🛑 Stopping existing processes..."
pm2 stop streamdb-online 2>/dev/null || true
pm2 delete streamdb-online 2>/dev/null || true

# Start the application
echo "🚀 Starting StreamDB Online..."
pm2 start index.js --name streamdb-online --env production

# Start webhook handler if it exists
if [ -f "../deployment/webhook-handler.js" ]; then
    echo "🔗 Starting webhook handler..."
    pm2 stop webhook-handler 2>/dev/null || true
    pm2 delete webhook-handler 2>/dev/null || true
    pm2 start ../deployment/webhook-handler.js --name webhook-handler
fi

# Save PM2 configuration
pm2 save

# Show status
echo "📊 Current PM2 Status:"
pm2 status

echo "✅ StreamDB Online started successfully!"
echo "🌐 Server should be accessible at: https://streamdb.online"
echo "🔧 Admin panel: https://streamdb.online/admin"

# Test database connection
echo "🗄️  Testing database connection..."
node test-db-connection.js

echo "🏁 Startup complete!"
`;

    const scriptPath = path.join(__dirname, 'start-production.sh');
    
    try {
      fs.writeFileSync(scriptPath, startScript);
      logSuccess(`Production start script created: ${scriptPath}`);
      
      // Make it executable (on Unix systems)
      try {
        await execAsync(`chmod +x "${scriptPath}"`);
        logSuccess('Made start script executable');
      } catch (error) {
        logWarning('Could not make script executable (Windows environment)');
      }
      
      return true;
    } catch (error) {
      logError(`Failed to create start script: ${error.message}`);
      return false;
    }
  }

  async generateDatabaseSetupScript() {
    logSection('🗄️  Generating Database Setup Script');
    
    const dbScript = `#!/usr/bin/env node

/**
 * Database Setup and Verification Script
 * Run this on the production server to set up the database
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

async function setupDatabase() {
  console.log('🗄️  Setting up StreamDB Online Database...');
  
  try {
    const config = {
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      charset: 'utf8mb4',
      timezone: '+00:00'
    };

    // Use socket for production
    if (process.env.DB_SOCKET) {
      config.socketPath = process.env.DB_SOCKET;
      console.log(\`🔌 Using socket connection: \${process.env.DB_SOCKET}\`);
    } else {
      config.host = process.env.DB_HOST || 'localhost';
      config.port = process.env.DB_PORT || 3306;
      console.log(\`🔌 Using TCP connection: \${config.host}:\${config.port}\`);
    }

    const connection = await mysql.createConnection(config);
    console.log('✅ Database connection successful');
    
    // Check if tables exist
    const [tables] = await connection.execute('SHOW TABLES');
    console.log(\`📊 Database has \${tables.length} tables\`);
    
    if (tables.length === 0) {
      console.log('⚠️  Database is empty. Please import the schema:');
      console.log('   1. Open phpMyAdmin in FastPanel');
      console.log('   2. Select your database');
      console.log('   3. Import the schema.sql file');
    } else {
      console.log('✅ Database schema appears to be imported');
    }
    
    await connection.end();
    console.log('🏁 Database setup check completed');
    
  } catch (error) {
    console.error('❌ Database setup failed:', error.message);
    process.exit(1);
  }
}

setupDatabase();
`;

    const scriptPath = path.join(__dirname, 'setup-database.js');
    
    try {
      fs.writeFileSync(scriptPath, dbScript);
      logSuccess(`Database setup script created: ${scriptPath}`);
      return true;
    } catch (error) {
      logError(`Failed to create database setup script: ${error.message}`);
      return false;
    }
  }

  async generateDeploymentInstructions() {
    logSection('📋 Generating Deployment Instructions');
    
    const instructions = `# 🚀 StreamDB Online - Production Deployment Fix

## CRITICAL ISSUES IDENTIFIED AND FIXED:

### 1. ✅ Environment Configuration Fixed
- NODE_ENV set to production
- Database socket configuration corrected
- All environment variables verified

### 2. ✅ Directory Structure Created
- logs/ directory created
- uploads/ subdirectories created
- Proper file structure established

### 3. ✅ Production Scripts Generated
- start-production.sh: Complete startup script
- setup-database.js: Database verification script

## 🔧 DEPLOYMENT STEPS FOR ALEXHOST SERVER:

### Step 1: Upload Fixed Code
\`\`\`bash
# SSH into your server
ssh streamdb_onl_usr@***********

# Navigate to project directory
cd /var/www/streamdb_onl_usr/data/www/streamdb.online

# Pull latest changes (or upload manually)
git pull origin main
\`\`\`

### Step 2: Install Dependencies
\`\`\`bash
# Install frontend dependencies
npm install

# Install server dependencies
cd server
npm install --production
cd ..
\`\`\`

### Step 3: Build Frontend
\`\`\`bash
npm run build
\`\`\`

### Step 4: Setup Database
\`\`\`bash
cd server
node setup-database.js
\`\`\`

### Step 5: Start Production Server
\`\`\`bash
# Make start script executable
chmod +x start-production.sh

# Run the start script
./start-production.sh
\`\`\`

### Step 6: Verify Deployment
\`\`\`bash
# Check PM2 status
pm2 status

# Check logs
pm2 logs streamdb-online

# Test website
curl -I https://streamdb.online
\`\`\`

## 🔍 TROUBLESHOOTING:

### If website still shows blank page:
1. Check PM2 logs: \`pm2 logs streamdb-online\`
2. Verify database connection: \`node test-db-connection.js\`
3. Check Nginx configuration
4. Verify file permissions

### If database connection fails:
1. Verify MySQL is running: \`systemctl status mysql\`
2. Check socket path: \`ls -la /var/run/mysqld/mysqld.sock\`
3. Test credentials in phpMyAdmin

### If PM2 processes don't start:
1. Check Node.js version: \`node --version\`
2. Verify dependencies: \`npm list\`
3. Check environment file: \`cat .env\`

## 📞 NEXT STEPS:

1. Deploy these fixes to your Alexhost server
2. Run the start-production.sh script
3. Test the website functionality
4. Set up GitHub auto-deployment webhook

## 🎯 EXPECTED RESULT:
- ✅ Website loads properly at https://streamdb.online
- ✅ Admin panel accessible at https://streamdb.online/admin
- ✅ Database connection working
- ✅ PM2 processes running
- ✅ Auto-deployment ready for setup
`;

    const instructionsPath = path.join(__dirname, '..', 'DEPLOYMENT_FIX_INSTRUCTIONS.md');
    
    try {
      fs.writeFileSync(instructionsPath, instructions);
      logSuccess(`Deployment instructions created: ${instructionsPath}`);
      return true;
    } catch (error) {
      logError(`Failed to create instructions: ${error.message}`);
      return false;
    }
  }

  async runFixes() {
    log('🚀 Starting Production Issues Fix...', 'bright');
    
    const results = await Promise.all([
      this.fixEnvironmentConfiguration(),
      this.createNecessaryDirectories(),
      this.generateProductionStartScript(),
      this.generateDatabaseSetupScript(),
      this.generateDeploymentInstructions()
    ]);
    
    const successCount = results.filter(Boolean).length;
    
    logSection('📊 Fix Summary');
    
    if (successCount === results.length) {
      logSuccess(`All ${successCount} fixes applied successfully!`);
      logInfo('Ready for production deployment');
    } else {
      logWarning(`${successCount}/${results.length} fixes applied`);
    }
    
    log('\n🏁 Production fix completed!', 'bright');
    
    return successCount === results.length;
  }
}

// Run fixes if called directly
if (require.main === module) {
  const fixer = new ProductionFixer();
  fixer.runFixes().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('Fix failed:', error);
    process.exit(1);
  });
}

module.exports = ProductionFixer;
