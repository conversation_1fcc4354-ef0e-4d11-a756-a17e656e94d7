const express = require('express');
const router = express.Router();
const db = require('../config/database');

/**
 * Simple Content API Routes
 * Bypasses complex database functions to provide working content endpoints
 */

// Get content with simple direct SQL queries
router.get('/', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 20;
    const page = parseInt(req.query.page) || 1;
    const offset = (page - 1) * limit;
    const type = req.query.type;
    const category = req.query.category;

    // Build simple SQL query
    let query = `
      SELECT
        c.id,
        c.title,
        c.description,
        c.type,
        c.year,
        c.image,
        c.poster_url as posterUrl,
        c.cover_image as coverImage,
        c.tags,
        c.imdb_rating,
        c.runtime,
        c.studio,
        c.is_published,
        c.is_featured,
        c.created_at,
        cat.name as category_name,
        cat.slug as category_slug
      FROM content c
      LEFT JOIN categories cat ON c.category_id = cat.id
      WHERE c.is_published = 1
    `;

    const params = [];

    // Add type filter
    if (type) {
      query += ' AND c.type = ?';
      params.push(type);
    }

    // Add category filter
    if (category) {
      query += ' AND cat.slug = ?';
      params.push(category);
    }

    // Add ordering and pagination
    query += ' ORDER BY c.created_at DESC LIMIT ? OFFSET ?';
    params.push(limit, offset);

    // Execute query
    const result = await db.execute(query, params);
    
    // Handle result format
    let content = [];
    if (Array.isArray(result)) {
      if (result.length > 0 && Array.isArray(result[0])) {
        content = result[0]; // [rows, fields] format
      } else {
        content = result; // Direct array format
      }
    }

    // Process tags as genres (convert from string to array if needed)
    content = content.map(item => {
      // Convert tags to genres array
      if (typeof item.tags === 'string' && item.tags) {
        try {
          // Try parsing as JSON first
          item.genres = JSON.parse(item.tags);
        } catch (e) {
          // If not JSON, split by comma
          item.genres = item.tags.split(',').map(tag => tag.trim()).filter(tag => tag);
        }
      } else {
        item.genres = [];
      }

      if (!Array.isArray(item.genres)) {
        item.genres = [];
      }

      return item;
    });

    // Get total count for pagination
    let countQuery = `
      SELECT COUNT(*) as total
      FROM content c
      LEFT JOIN categories cat ON c.category_id = cat.id
      WHERE c.is_published = 1
    `;

    const countParams = [];
    if (type) {
      countQuery += ' AND c.type = ?';
      countParams.push(type);
    }
    if (category) {
      countQuery += ' AND cat.slug = ?';
      countParams.push(category);
    }

    const countResult = await db.execute(countQuery, countParams);
    let totalCount = 0;
    
    if (Array.isArray(countResult)) {
      if (countResult.length > 0 && Array.isArray(countResult[0])) {
        totalCount = countResult[0][0]?.total || 0;
      } else if (countResult[0]?.total !== undefined) {
        totalCount = countResult[0].total;
      }
    }

    const totalPages = Math.ceil(totalCount / limit);

    res.json({
      success: true,
      data: content,
      pagination: {
        page,
        limit,
        total: totalCount,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      },
      filters: {
        type,
        category
      }
    });

  } catch (error) {
    console.error('Simple content API error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch content',
      message: error.message
    });
  }
});

// Get single content item by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const query = `
      SELECT 
        c.*,
        cat.name as category_name,
        cat.slug as category_slug
      FROM content c
      LEFT JOIN categories cat ON c.category_id = cat.id
      WHERE c.id = ? AND c.is_published = 1
    `;

    const result = await db.execute(query, [id]);
    
    let content = null;
    if (Array.isArray(result)) {
      if (result.length > 0 && Array.isArray(result[0])) {
        content = result[0][0] || null;
      } else {
        content = result[0] || null;
      }
    }

    if (!content) {
      return res.status(404).json({
        success: false,
        error: 'Content not found'
      });
    }

    // Process tags as genres
    if (typeof content.tags === 'string' && content.tags) {
      try {
        content.genres = JSON.parse(content.tags);
      } catch (e) {
        content.genres = content.tags.split(',').map(tag => tag.trim()).filter(tag => tag);
      }
    } else {
      content.genres = [];
    }
    if (!Array.isArray(content.genres)) {
      content.genres = [];
    }

    res.json({
      success: true,
      data: content
    });

  } catch (error) {
    console.error('Simple content by ID error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch content',
      message: error.message
    });
  }
});

// Get featured content
router.get('/featured/list', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 5;

    const query = `
      SELECT 
        c.id,
        c.title,
        c.description,
        c.type,
        c.year,
        c.image,
        c.poster_url as posterUrl,
        c.cover_image as coverImage,
        c.tags,
        cat.name as category_name,
        cat.slug as category_slug
      FROM content c
      LEFT JOIN categories cat ON c.category_id = cat.id
      WHERE c.is_published = 1 AND c.is_featured = 1
      ORDER BY c.created_at DESC
      LIMIT ?
    `;

    const result = await db.execute(query, [limit]);
    
    let content = [];
    if (Array.isArray(result)) {
      if (result.length > 0 && Array.isArray(result[0])) {
        content = result[0];
      } else {
        content = result;
      }
    }

    // Process tags as genres
    content = content.map(item => {
      if (typeof item.tags === 'string' && item.tags) {
        try {
          item.genres = JSON.parse(item.tags);
        } catch (e) {
          item.genres = item.tags.split(',').map(tag => tag.trim()).filter(tag => tag);
        }
      } else {
        item.genres = [];
      }
      if (!Array.isArray(item.genres)) {
        item.genres = [];
      }
      return item;
    });

    res.json({
      success: true,
      data: content
    });

  } catch (error) {
    console.error('Featured content error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch featured content',
      message: error.message
    });
  }
});

module.exports = router;
