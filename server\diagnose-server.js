#!/usr/bin/env node

/**
 * StreamDB Online - Server Diagnostic Tool
 * Comprehensive server status check and issue identification
 */

const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const util = require('util');
const execAsync = util.promisify(exec);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSection(title) {
  console.log('\n' + '='.repeat(60));
  log(title, 'cyan');
  console.log('='.repeat(60));
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

class ServerDiagnostic {
  constructor() {
    this.issues = [];
    this.warnings = [];
    this.successes = [];
  }

  addIssue(message) {
    this.issues.push(message);
    logError(message);
  }

  addWarning(message) {
    this.warnings.push(message);
    logWarning(message);
  }

  addSuccess(message) {
    this.successes.push(message);
    logSuccess(message);
  }

  async checkEnvironmentFile() {
    logSection('🔧 Environment Configuration Check');
    
    const envPath = path.join(__dirname, '.env');
    
    if (!fs.existsSync(envPath)) {
      this.addIssue('Environment file (.env) not found');
      return;
    }

    this.addSuccess('Environment file exists');

    try {
      const envContent = fs.readFileSync(envPath, 'utf8');
      const envVars = {};
      
      envContent.split('\n').forEach(line => {
        const [key, value] = line.split('=');
        if (key && value) {
          envVars[key.trim()] = value.trim();
        }
      });

      // Check critical environment variables
      const criticalVars = [
        'DB_HOST', 'DB_USER', 'DB_PASSWORD', 'DB_NAME',
        'JWT_SECRET', 'SESSION_SECRET', 'NODE_ENV'
      ];

      criticalVars.forEach(varName => {
        if (envVars[varName]) {
          this.addSuccess(`${varName} is configured`);
        } else {
          this.addIssue(`${varName} is missing from environment`);
        }
      });

      // Check NODE_ENV specifically
      if (envVars.NODE_ENV === 'production') {
        this.addSuccess('NODE_ENV is set to production');
      } else {
        this.addIssue(`NODE_ENV is set to '${envVars.NODE_ENV}' instead of 'production'`);
      }

      // Check database socket configuration
      if (envVars.DB_SOCKET) {
        this.addSuccess(`Database socket configured: ${envVars.DB_SOCKET}`);
      } else {
        this.addWarning('Database socket not configured, using TCP connection');
      }

    } catch (error) {
      this.addIssue(`Failed to read environment file: ${error.message}`);
    }
  }

  async checkDatabaseConnection() {
    logSection('🗄️  Database Connection Check');
    
    try {
      require('dotenv').config();
      const mysql = require('mysql2/promise');
      
      const config = {
        user: process.env.DB_USER,
        password: process.env.DB_PASSWORD,
        database: process.env.DB_NAME,
        charset: 'utf8mb4',
        timezone: '+00:00'
      };

      // Use socket if specified
      if (process.env.DB_SOCKET) {
        config.socketPath = process.env.DB_SOCKET;
        logInfo(`Attempting socket connection: ${process.env.DB_SOCKET}`);
      } else {
        config.host = process.env.DB_HOST || 'localhost';
        config.port = process.env.DB_PORT || 3306;
        logInfo(`Attempting TCP connection: ${config.host}:${config.port}`);
      }

      const connection = await mysql.createConnection(config);
      this.addSuccess('Database connection successful');
      
      // Test basic query
      const [rows] = await connection.execute('SELECT 1 as test');
      this.addSuccess('Database query test successful');
      
      // Check if tables exist
      const [tables] = await connection.execute('SHOW TABLES');
      if (tables.length > 0) {
        this.addSuccess(`Database has ${tables.length} tables`);
      } else {
        this.addWarning('Database has no tables - schema may not be imported');
      }
      
      await connection.end();
      
    } catch (error) {
      this.addIssue(`Database connection failed: ${error.message}`);
    }
  }

  async checkPM2Status() {
    logSection('🔄 PM2 Process Status');
    
    try {
      const { stdout } = await execAsync('pm2 list');
      logInfo('PM2 Process List:');
      console.log(stdout);
      
      // Check for specific processes
      if (stdout.includes('streamdb-online')) {
        this.addSuccess('StreamDB Online process found in PM2');
      } else {
        this.addIssue('StreamDB Online process not found in PM2');
      }
      
      if (stdout.includes('webhook-handler')) {
        this.addSuccess('Webhook handler process found in PM2');
      } else {
        this.addWarning('Webhook handler process not found in PM2');
      }
      
    } catch (error) {
      this.addIssue(`Failed to check PM2 status: ${error.message}`);
    }
  }

  async checkServerLogs() {
    logSection('📋 Server Logs Check');
    
    const logPaths = [
      path.join(__dirname, 'logs', 'server.log'),
      '/var/log/streamdb-deploy.log'
    ];
    
    for (const logPath of logPaths) {
      if (fs.existsSync(logPath)) {
        this.addSuccess(`Log file exists: ${logPath}`);
        try {
          const logContent = fs.readFileSync(logPath, 'utf8');
          const lines = logContent.split('\n').slice(-10); // Last 10 lines
          logInfo(`Last 10 lines from ${logPath}:`);
          lines.forEach(line => {
            if (line.trim()) {
              console.log(`  ${line}`);
            }
          });
        } catch (error) {
          this.addWarning(`Could not read log file: ${error.message}`);
        }
      } else {
        this.addWarning(`Log file not found: ${logPath}`);
      }
    }
  }

  async checkFilePermissions() {
    logSection('🔒 File Permissions Check');
    
    const criticalPaths = [
      { path: path.join(__dirname, '.env'), expectedMode: '600' },
      { path: path.join(__dirname, 'uploads'), expectedMode: '755' },
      { path: path.join(__dirname, 'logs'), expectedMode: '755' }
    ];
    
    for (const { path: filePath, expectedMode } of criticalPaths) {
      if (fs.existsSync(filePath)) {
        try {
          const stats = fs.statSync(filePath);
          const mode = (stats.mode & parseInt('777', 8)).toString(8);
          
          if (mode === expectedMode) {
            this.addSuccess(`${filePath} has correct permissions (${mode})`);
          } else {
            this.addWarning(`${filePath} has permissions ${mode}, expected ${expectedMode}`);
          }
        } catch (error) {
          this.addWarning(`Could not check permissions for ${filePath}: ${error.message}`);
        }
      } else {
        this.addWarning(`Path does not exist: ${filePath}`);
      }
    }
  }

  async checkNetworkConnectivity() {
    logSection('🌐 Network Connectivity Check');
    
    try {
      // Check if server is listening on expected ports
      const { stdout } = await execAsync('netstat -tlnp 2>/dev/null | grep -E ":300[0-9]|:900[0-9]" || true');
      
      if (stdout.includes(':3001')) {
        this.addSuccess('Server is listening on port 3001');
      } else {
        this.addIssue('Server is not listening on port 3001');
      }
      
      if (stdout.includes(':9000')) {
        this.addSuccess('Webhook handler is listening on port 9000');
      } else {
        this.addWarning('Webhook handler is not listening on port 9000');
      }
      
      logInfo('Active network listeners:');
      console.log(stdout || 'No relevant listeners found');
      
    } catch (error) {
      this.addWarning(`Could not check network connectivity: ${error.message}`);
    }
  }

  async checkBuildStatus() {
    logSection('🏗️  Build Status Check');

    const distPath = path.join(__dirname, '..', 'dist');

    if (fs.existsSync(distPath)) {
      this.addSuccess('Frontend build directory exists');

      const indexPath = path.join(distPath, 'index.html');
      if (fs.existsSync(indexPath)) {
        this.addSuccess('Frontend index.html exists');
      } else {
        this.addIssue('Frontend index.html not found');
      }

      const assetsPath = path.join(distPath, 'assets');
      if (fs.existsSync(assetsPath)) {
        const assets = fs.readdirSync(assetsPath);
        this.addSuccess(`Frontend assets directory contains ${assets.length} files`);
      } else {
        this.addIssue('Frontend assets directory not found');
      }

    } else {
      this.addIssue('Frontend build directory (dist) not found - run npm run build');
    }
  }

  async generateSummary() {
    logSection('📊 Diagnostic Summary');

    console.log(`\n${colors.green}✅ Successes: ${this.successes.length}${colors.reset}`);
    console.log(`${colors.yellow}⚠️  Warnings: ${this.warnings.length}${colors.reset}`);
    console.log(`${colors.red}❌ Critical Issues: ${this.issues.length}${colors.reset}`);

    if (this.issues.length > 0) {
      console.log(`\n${colors.red}${colors.bright}CRITICAL ISSUES TO FIX:${colors.reset}`);
      this.issues.forEach((issue, index) => {
        console.log(`${colors.red}${index + 1}. ${issue}${colors.reset}`);
      });
    }

    if (this.warnings.length > 0) {
      console.log(`\n${colors.yellow}${colors.bright}WARNINGS TO REVIEW:${colors.reset}`);
      this.warnings.forEach((warning, index) => {
        console.log(`${colors.yellow}${index + 1}. ${warning}${colors.reset}`);
      });
    }

    console.log(`\n${colors.cyan}${colors.bright}RECOMMENDED ACTIONS:${colors.reset}`);

    if (this.issues.some(issue => issue.includes('NODE_ENV'))) {
      console.log(`${colors.blue}1. Fix NODE_ENV in .env file: Set NODE_ENV=production${colors.reset}`);
    }

    if (this.issues.some(issue => issue.includes('Database connection'))) {
      console.log(`${colors.blue}2. Fix database connection: Check credentials and socket path${colors.reset}`);
    }

    if (this.issues.some(issue => issue.includes('PM2'))) {
      console.log(`${colors.blue}3. Start PM2 processes: pm2 start server/index.js --name streamdb-online${colors.reset}`);
    }

    if (this.issues.some(issue => issue.includes('build'))) {
      console.log(`${colors.blue}4. Build frontend: npm run build${colors.reset}`);
    }

    console.log(`${colors.blue}5. Restart all services after fixes${colors.reset}`);
  }

  async runDiagnostic() {
    log('🚀 Starting StreamDB Online Server Diagnostic...', 'bright');

    await this.checkEnvironmentFile();
    await this.checkDatabaseConnection();
    await this.checkBuildStatus();
    await this.checkPM2Status();
    await this.checkNetworkConnectivity();
    await this.checkServerLogs();
    await this.checkFilePermissions();
    await this.generateSummary();

    log('\n🏁 Diagnostic completed!', 'bright');

    return {
      issues: this.issues.length,
      warnings: this.warnings.length,
      successes: this.successes.length
    };
  }
}

// Run diagnostic if called directly
if (require.main === module) {
  const diagnostic = new ServerDiagnostic();
  diagnostic.runDiagnostic().then(result => {
    process.exit(result.issues > 0 ? 1 : 0);
  }).catch(error => {
    console.error('Diagnostic failed:', error);
    process.exit(1);
  });
}

module.exports = ServerDiagnostic;
