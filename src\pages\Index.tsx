
import React, { useState, useEffect } from "react";
import HeroCarousel from "@/components/HeroCarousel";
import CardGrid from "@/components/CardGrid";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import AdBlockerAwarenessPopup from "@/components/AdBlockerAwarenessPopup";
import AdBlockerDetectionModal from "@/components/AdBlockerDetectionModal";
import ImageDiagnostics from "@/components/ImageDiagnostics";
import { MediaItem } from "@/types/media";
import { Link } from "react-router-dom";
import PromoBannerContainer from "@/components/PromoBannerContainer";
import { scrollToTop } from "@/utils/scrollToTop";
import { getHomepageContent } from "@/utils/contentFilters";
import {
  shouldShowAwarenessPopup,
  recordPopupShown,
  recordPopupDismissed
} from "@/utils/adBlockerAwarenessTracking";
import { Button } from "@/components/ui/button";
import apiService from "@/services/apiService";

const Index = () => {
  // State for content data
  const [contentData, setContentData] = useState<MediaItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Get properly filtered content for homepage sections
  const { movies, series, requested, featured, carousel } = getHomepageContent(contentData, 20);

  // Ad blocker awareness popup state
  const [showAwarenessPopup, setShowAwarenessPopup] = useState(false);

  // Test modal state (development only)
  const [showTestModal, setShowTestModal] = useState(false);
  const testModalData = {
    hasAdBlocker: true,
    detectedBlockers: [{
      name: 'Test Ad Blocker',
      type: 'extension' as const,
      detected: true,
      confidence: 90,
      detectionMethod: 'Homepage Test',
      disableInstructions: ['Test instruction 1', 'Test instruction 2'],
      whitelistInstructions: ['Test whitelist 1', 'Test whitelist 2']
    }],
    totalConfidence: 90,
    detectionMethods: ['test'],
    timestamp: Date.now()
  };

  // Fetch content data from API
  useEffect(() => {
    const fetchContent = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await apiService.getContent({
          published: true,
          limit: 100 // Get enough content for all sections
        });

        if (response && response.success && Array.isArray(response.data)) {
          setContentData(response.data);
        } else {
          console.warn('No content data received from API, using empty array');
          setContentData([]);
        }
      } catch (error) {
        console.error('Error fetching content:', error);
        setError('Failed to load content');
        setContentData([]); // Fallback to empty array
      } finally {
        setLoading(false);
      }
    };

    fetchContent();
  }, []);

  // Check if popup should be shown and set up delayed display
  useEffect(() => {
    const isProduction = import.meta.env.PROD;
    const debugPrefix = isProduction ? '🏠 [PROD]' : '🏠 [DEV]';

    console.log(`${debugPrefix} Homepage useEffect triggered`);
    console.log(`${debugPrefix} Current pathname:`, window.location.pathname);
    console.log(`${debugPrefix} Environment:`, { isProduction, href: window.location.href });

    // Only run on homepage
    if (window.location.pathname !== '/') {
      console.log(`${debugPrefix} Not on homepage, skipping awareness popup`);
      return;
    }

    // Check if popup should be shown based on tracking
    const shouldShow = shouldShowAwarenessPopup();
    console.log(`${debugPrefix} Should show awareness popup:`, shouldShow);

    if (shouldShow) {
      console.log(`${debugPrefix} Setting up awareness popup timer (2.5s delay)`);
      // Delay popup appearance for better UX (2-3 seconds)
      const showTimer = setTimeout(() => {
        console.log(`${debugPrefix} Showing awareness popup now`);
        setShowAwarenessPopup(true);
        recordPopupShown();
      }, 2500); // 2.5 seconds delay

      return () => {
        console.log(`${debugPrefix} Cleaning up awareness popup timer`);
        clearTimeout(showTimer);
      };
    } else {
      console.log(`${debugPrefix} Awareness popup not needed at this time`);
    }
  }, []);

  const handlePopupDismiss = () => {
    setShowAwarenessPopup(false);
    recordPopupDismissed();
  };

  // Show loading state
  if (loading) {
    return (
      <div className="flex flex-col min-h-screen bg-background">
        <Header />
        <div className="h-7 md:h-10" />
        <main className="flex-1 w-full max-w-7xl mx-auto px-3 sm:px-4 py-2 sm:py-4">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center text-muted-foreground">
              <div className="text-4xl mb-4">🎬</div>
              <p className="text-lg">Loading content...</p>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="flex flex-col min-h-screen bg-background">
        <Header />
        <div className="h-7 md:h-10" />
        <main className="flex-1 w-full max-w-7xl mx-auto px-3 sm:px-4 py-2 sm:py-4">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center text-muted-foreground">
              <div className="text-4xl mb-4">⚠️</div>
              <p className="text-lg mb-2">Failed to load content</p>
              <p className="text-sm">{error}</p>
              <Button
                onClick={() => window.location.reload()}
                className="mt-4"
              >
                Retry
              </Button>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen bg-background">
      <Header />

      {/* Added spacing here after Header */}
      <div className="h-7 md:h-10" />

      <main className="flex-1 w-full max-w-7xl mx-auto px-3 sm:px-4 py-2 sm:py-4">
        {/* Increased margin-bottom for HeroCarousel */}
        <div className="mb-6 sm:mb-9 md:mb-14">
          <HeroCarousel contentData={contentData} />
        </div>

        {/* Proper gap between sections */}
        <div className="mb-8 sm:mb-12">
          <PromoBannerContainer />
        </div>

        {/* Movies Section */}
        <section className="stdb-section mb-10 sm:mb-14">
          <div className="flex items-end justify-between mb-3 sm:mb-4">
            <h3
              className="
                text-2xl sm:text-3xl md:text-4xl
                font-black
                font-mono
                text-primary
                drop-shadow-[0_2px_10px_rgba(34,197,94,0.4)]
                tracking-wide
                uppercase
                leading-tight
                select-none
                "
              style={{
                letterSpacing: "1px",
              }}
            >
              Movies
            </h3>
            <Link
              to="/movies"
              className="text-primary font-bold underline underline-offset-4 text-sm sm:text-base hover:opacity-80 transition flex-shrink-0"
              onClick={scrollToTop}
            >
              Show all
            </Link>
          </div>
          <CardGrid items={movies} />
        </section>

        {/* Web Series Section */}
        <section className="stdb-section mb-10 sm:mb-14">
          <div className="flex items-end justify-between mb-3 sm:mb-4">
            <h3
              className="
                text-2xl sm:text-3xl md:text-4xl
                font-black
                font-mono
                text-primary
                drop-shadow-[0_2px_10px_rgba(34,197,94,0.4)]
                tracking-wide
                uppercase
                leading-tight
                select-none
                "
              style={{
                letterSpacing: "1px",
              }}
            >
              Web Series
            </h3>
            <Link
              to="/series"
              className="text-primary font-bold underline underline-offset-4 text-sm sm:text-base hover:opacity-80 transition flex-shrink-0"
              onClick={scrollToTop}
            >
              Show all
            </Link>
          </div>
          <CardGrid items={series} />
        </section>

        {/* Requested Section */}
        <section className="stdb-section mb-12 sm:mb-16">
          <div className="flex items-end justify-between mb-3 sm:mb-4">
            <h3
              className="
                text-2xl sm:text-3xl md:text-4xl
                font-black
                font-mono
                text-primary
                drop-shadow-[0_2px_10px_rgba(34,197,94,0.4)]
                tracking-wide
                uppercase
                leading-tight
                select-none
                "
              style={{
                letterSpacing: "1px",
              }}
            >
              Requested
            </h3>
            <Link
              to="/requested"
              className="text-primary font-bold underline underline-offset-4 text-sm sm:text-base hover:opacity-80 transition flex-shrink-0"
              onClick={scrollToTop}
            >
              Show all
            </Link>
          </div>
          <CardGrid items={requested} />
        </section>
      </main>

      <Footer />

      {/* Development Test Button */}
      {process.env.NODE_ENV === 'development' && (
        <Button
          onClick={() => {
            console.log('🧪 Testing modal from homepage');
            setShowTestModal(true);
          }}
          className="fixed bottom-4 left-4 z-50 bg-purple-600 hover:bg-purple-700"
        >
          🧪 Test Modal
        </Button>
      )}

      {/* Ad Blocker Awareness Popup */}
      <AdBlockerAwarenessPopup
        isVisible={showAwarenessPopup}
        onDismiss={handlePopupDismiss}
        autoCloseDelay={17000} // 17 seconds
      />

      {/* Test Modal */}
      {showTestModal && (
        <AdBlockerDetectionModal
          isOpen={showTestModal}
          onClose={() => setShowTestModal(false)}
          detectionResult={testModalData}
          onRetry={() => console.log('Test retry')}
          onProceed={() => console.log('Test proceed')}
          onDismissAndProceed={() => {
            console.log('Test dismiss and proceed');
            setShowTestModal(false);
          }}
        />
      )}
    </div>
  );
};

export default Index;
