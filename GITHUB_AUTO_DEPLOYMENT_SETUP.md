# GitHub Auto-Deployment Setup Guide

## 🎯 **Complete GitHub Webhook Auto-Deployment System**

Your codebase already has a comprehensive auto-deployment system! Here's how to make it fully functional:

## 📋 **Current Setup Status**

### ✅ **Already Configured:**
- ✅ Webhook server (`server/services/webhook-server.js`)
- ✅ Webhook routes with security (`server/routes/webhook.js`)
- ✅ Security middleware (`server/middleware/webhookSecurity.js`)
- ✅ Deployment script (`deployment/deploy.sh`)
- ✅ Environment variables (`.env`)
- ✅ Database logging and monitoring

### 🔧 **Needs Configuration:**
- GitHub repository webhook URL
- Server deployment and startup
- Port configuration for Alexhost VPS

## 🚀 **Step-by-Step Setup**

### **Step 1: Configure GitHub Webhook**

1. **Go to your GitHub repository:**
   ```
   https://github.com/aakash171088/Streaming_DB
   ```

2. **Navigate to Settings → Webhooks → Add webhook**

3. **Configure webhook settings:**
   ```
   Payload URL: https://streamdb.online/api/webhook/github
   Content type: application/json
   Secret: 249fd45813bc97e48c46d634418b77a3c3388d219b9ffcfa9e7edaaa7c28a0ef
   ```

4. **Select events:**
   - ✅ Just the push event
   - ✅ Active

5. **Click "Add webhook"**

### **Step 2: Server Configuration**

Your Alexhost VPS needs these services running:

#### **A. Main Application Server**
```bash
# Navigate to project directory
cd /var/www/streamdb_onl_usr/data/www/streamdb.online

# Install dependencies
npm install
cd server && npm install && cd ..

# Start main application with PM2
pm2 start server/index.js --name "streamdb-online" --env production

# Save PM2 configuration
pm2 save
pm2 startup
```

#### **B. Webhook Server (Port Issue Fix)**
Since Alexhost only allows ports 22, 80, 443, we need to configure Apache to proxy webhook requests:

**Apache Configuration** (`/etc/apache2/sites-available/streamdb.online.conf`):
```apache
<VirtualHost *:80>
    ServerName streamdb.online
    DocumentRoot /var/www/streamdb_onl_usr/data/www/streamdb.online/dist
    
    # Proxy API requests to Node.js
    ProxyPreserveHost On
    ProxyPass /api/ http://localhost:3001/api/
    ProxyPassReverse /api/ http://localhost:3001/api/
    
    # Proxy webhook requests to webhook server
    ProxyPass /api/webhook/ http://localhost:9000/api/webhook/
    ProxyPassReverse /api/webhook/ http://localhost:9000/api/webhook/
    
    # Enable proxy modules
    LoadModule proxy_module modules/mod_proxy.so
    LoadModule proxy_http_module modules/mod_proxy_http.so
</VirtualHost>
```

#### **C. Start Webhook Server**
```bash
# Start webhook server
pm2 start server/services/webhook-server.js --name "webhook-handler" --env production

# Verify both services are running
pm2 list
```

### **Step 3: Test the Setup**

#### **A. Test Webhook Connectivity**
```bash
# Test webhook endpoint
curl -X GET https://streamdb.online/api/webhook/test

# Expected response:
{
  "webhook_url": "https://streamdb.online/api/webhook/github",
  "webhook_secret_configured": true,
  "deploy_script_exists": true,
  "database_connected": true
}
```

#### **B. Test GitHub Webhook**
1. Make a small change to your repository
2. Commit and push to `New-Main-1` branch
3. Check webhook delivery in GitHub Settings → Webhooks
4. Monitor deployment logs:
   ```bash
   tail -f /var/log/streamdb-deploy.log
   pm2 logs webhook-handler
   ```

### **Step 4: Deployment Process**

When you push to GitHub, this happens automatically:

1. **GitHub sends webhook** → `https://streamdb.online/api/webhook/github`
2. **Security validation** → Signature verification, rate limiting
3. **Payload validation** → Branch check (`New-Main-1`), repository verification
4. **Backup creation** → Current version backed up
5. **Code update** → `git pull` latest changes
6. **Dependencies** → `npm install` for frontend and backend
7. **Build** → `npm run build` for frontend
8. **Security audit** → Automated security checks
9. **Service restart** → PM2 restart with zero downtime
10. **Logging** → Complete deployment log in database

## 🔒 **Security Features**

### **Built-in Security:**
- ✅ **Signature Verification** - GitHub webhook secret validation
- ✅ **Rate Limiting** - Max 10 deployments per hour
- ✅ **IP Whitelisting** - GitHub IP ranges only
- ✅ **Payload Validation** - JSON structure and repository verification
- ✅ **Branch Protection** - Only `New-Main-1` branch triggers deployment
- ✅ **Database Logging** - All events logged for audit
- ✅ **Backup System** - Automatic backup before each deployment
- ✅ **Rollback Capability** - Quick rollback to previous version

### **Admin Panel Integration:**
Access deployment management at: `https://streamdb.online/admin`

- 📊 **Deployment Status** - View recent deployments
- 📋 **Deployment Logs** - Detailed logging and monitoring
- 🔄 **Manual Deployment** - Trigger deployment manually
- ⏪ **Rollback** - Rollback to previous version
- 🧪 **Test Webhook** - Test webhook connectivity

## 📱 **Usage Instructions**

### **Automatic Deployment:**
1. Make changes to your code
2. Commit changes: `git commit -m "Your changes"`
3. Push to main branch: `git push origin New-Main-1`
4. Deployment happens automatically within 30 seconds
5. Check deployment status in admin panel

### **Manual Deployment:**
```bash
# SSH to your server
ssh streamdb_onl_usr@45.93.8.197

# Navigate to project
cd /var/www/streamdb_onl_usr/data/www/streamdb.online

# Manual deployment
./deployment/deploy.sh deploy

# Check status
./deployment/deploy.sh status

# Rollback if needed
./deployment/deploy.sh rollback
```

## 🔧 **Troubleshooting**

### **Common Issues:**

1. **Webhook not receiving requests:**
   ```bash
   # Check Apache proxy configuration
   sudo apache2ctl configtest
   sudo systemctl reload apache2
   
   # Check webhook server
   pm2 logs webhook-handler
   ```

2. **Deployment fails:**
   ```bash
   # Check deployment logs
   tail -f /var/log/streamdb-deploy.log
   
   # Check PM2 processes
   pm2 list
   pm2 logs streamdb-online
   ```

3. **Permission issues:**
   ```bash
   # Fix permissions
   sudo chown -R streamdb_onl_usr:streamdb_onl_usr /var/www/streamdb_onl_usr/data/www/streamdb.online
   chmod +x deployment/deploy.sh
   ```

## 🎉 **Benefits of This Setup**

- ✅ **Zero Downtime Deployments** - PM2 handles graceful restarts
- ✅ **Automatic Backups** - Every deployment creates a backup
- ✅ **Security First** - Multiple layers of security validation
- ✅ **Complete Logging** - Full audit trail of all deployments
- ✅ **Easy Rollback** - One-command rollback to previous version
- ✅ **Admin Integration** - Full control through admin panel
- ✅ **Rate Limited** - Prevents deployment spam
- ✅ **Branch Protected** - Only specific branch triggers deployment

## 🚀 **Next Steps**

1. **Configure GitHub webhook** with the provided URL and secret
2. **Start the webhook server** on your VPS
3. **Configure Apache proxy** for webhook routing
4. **Test with a small commit** to verify everything works
5. **Monitor the first deployment** through logs and admin panel

Your auto-deployment system is enterprise-grade and ready for production use!
