#!/usr/bin/env node

/**
 * GitHub Webhook Test Script
 * Tests webhook functionality and simulates GitHub webhook calls
 */

const crypto = require('crypto');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

class WebhookTester {
  constructor() {
    this.webhookSecret = process.env.WEBHOOK_SECRET;
    this.webhookUrl = process.env.FRONTEND_URL + '/api/webhook/github';
    this.testPayload = {
      ref: 'refs/heads/New-Main-1',
      repository: {
        full_name: 'aakash171088/Streaming_DB',
        name: 'Streaming_DB'
      },
      pusher: {
        name: 'test-user'
      },
      commits: [
        {
          id: 'abc123',
          message: 'Test commit for webhook validation',
          author: {
            name: 'Test User',
            email: '<EMAIL>'
          }
        }
      ]
    };
  }

  generateSignature(payload) {
    if (!this.webhookSecret) {
      throw new Error('WEBHOOK_SECRET not configured');
    }

    const payloadString = JSON.stringify(payload);
    const signature = 'sha256=' + crypto
      .createHmac('sha256', this.webhookSecret)
      .update(payloadString, 'utf8')
      .digest('hex');

    return { signature, payloadString };
  }

  async testWebhookEndpoint() {
    console.log('🧪 Testing GitHub Webhook Endpoint\n');

    try {
      // Test 1: Check webhook secret configuration
      console.log('1️⃣ Testing webhook secret configuration...');
      if (!this.webhookSecret) {
        console.log('❌ WEBHOOK_SECRET not configured in environment');
        return false;
      }
      console.log('✅ WEBHOOK_SECRET is configured');

      // Test 2: Generate test signature
      console.log('\n2️⃣ Generating test webhook signature...');
      const { signature, payloadString } = this.generateSignature(this.testPayload);
      console.log('✅ Test signature generated successfully');
      console.log(`   Signature: ${signature.substring(0, 20)}...`);

      // Test 3: Test webhook endpoint accessibility
      console.log('\n3️⃣ Testing webhook endpoint accessibility...');
      
      try {
        const response = await fetch(this.webhookUrl.replace('/github', '/health'), {
          method: 'GET',
          headers: {
            'User-Agent': 'StreamDB-Webhook-Tester/1.0'
          }
        });

        if (response.ok) {
          const data = await response.json();
          console.log('✅ Webhook health endpoint accessible');
          console.log('   Response:', data);
        } else {
          console.log(`❌ Webhook health endpoint returned ${response.status}`);
          const text = await response.text();
          console.log('   Error:', text);
        }
      } catch (error) {
        console.log('❌ Cannot reach webhook health endpoint');
        console.log('   Error:', error.message);
      }

      // Test 4: Simulate GitHub webhook call
      console.log('\n4️⃣ Simulating GitHub webhook call...');
      
      try {
        const response = await fetch(this.webhookUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Hub-Signature-256': signature,
            'X-GitHub-Event': 'push',
            'User-Agent': 'GitHub-Hookshot/test'
          },
          body: payloadString
        });

        console.log(`   Response Status: ${response.status}`);
        
        const responseText = await response.text();
        console.log(`   Response Body: ${responseText}`);

        if (response.ok) {
          console.log('✅ Webhook call successful');
          return true;
        } else {
          console.log('❌ Webhook call failed');
          return false;
        }
      } catch (error) {
        console.log('❌ Webhook call error:', error.message);
        return false;
      }

    } catch (error) {
      console.log('❌ Webhook test failed:', error.message);
      return false;
    }
  }

  async testDeploymentScript() {
    console.log('\n🚀 Testing Deployment Script\n');

    const deployScript = process.env.DEPLOY_SCRIPT;
    
    if (!deployScript) {
      console.log('❌ DEPLOY_SCRIPT not configured');
      return false;
    }

    if (!fs.existsSync(deployScript)) {
      console.log(`❌ Deploy script not found: ${deployScript}`);
      return false;
    }

    console.log('✅ Deploy script exists');
    
    try {
      const stats = fs.statSync(deployScript);
      const isExecutable = !!(stats.mode & parseInt('100', 8));
      
      if (isExecutable) {
        console.log('✅ Deploy script is executable');
      } else {
        console.log('⚠️  Deploy script is not executable, fixing...');
        fs.chmodSync(deployScript, '755');
        console.log('✅ Deploy script made executable');
      }

      // Test script syntax
      const { exec } = require('child_process');
      const { promisify } = require('util');
      const execAsync = promisify(exec);

      try {
        await execAsync(`bash -n "${deployScript}"`);
        console.log('✅ Deploy script syntax is valid');
      } catch (error) {
        console.log('❌ Deploy script has syntax errors:', error.message);
        return false;
      }

      // Test script status command
      try {
        const { stdout } = await execAsync(`bash "${deployScript}" status`, { timeout: 30000 });
        console.log('✅ Deploy script status command works');
        console.log('   Output preview:', stdout.substring(0, 200) + '...');
      } catch (error) {
        console.log('⚠️  Deploy script status command failed:', error.message);
      }

      return true;
    } catch (error) {
      console.log('❌ Deploy script test failed:', error.message);
      return false;
    }
  }

  async testDatabaseTables() {
    console.log('\n🗄️ Testing Database Tables\n');

    try {
      const db = require('../config/database');
      
      if (!db || !db.query) {
        console.log('❌ Database connection not available');
        return false;
      }

      // Test required tables
      const requiredTables = [
        'deployments',
        'deployment_logs',
        'manual_deployments'
      ];

      for (const table of requiredTables) {
        try {
          await new Promise((resolve, reject) => {
            db.query(`SHOW TABLES LIKE '${table}'`, (err, results) => {
              if (err) reject(err);
              else resolve(results);
            });
          });
          console.log(`✅ Table '${table}' exists`);
        } catch (error) {
          console.log(`❌ Table '${table}' missing or inaccessible`);
        }
      }

      // Test database write permissions
      try {
        await new Promise((resolve, reject) => {
          db.query(
            'INSERT INTO deployment_logs (level, message, data) VALUES (?, ?, ?)',
            ['INFO', 'Webhook test', JSON.stringify({ test: true, timestamp: new Date() })],
            (err, result) => {
              if (err) reject(err);
              else resolve(result);
            }
          );
        });
        console.log('✅ Database write permissions working');
      } catch (error) {
        console.log('❌ Database write test failed:', error.message);
      }

      return true;
    } catch (error) {
      console.log('❌ Database test failed:', error.message);
      return false;
    }
  }

  async runAllTests() {
    console.log('🔧 GitHub Webhook Diagnostic Tool\n');
    console.log('=' .repeat(50));

    const results = {
      webhook: await this.testWebhookEndpoint(),
      deployment: await this.testDeploymentScript(),
      database: await this.testDatabaseTables()
    };

    console.log('\n📊 Test Summary:');
    console.log('=' .repeat(50));
    console.log(`Webhook Endpoint: ${results.webhook ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Deployment Script: ${results.deployment ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Database Tables: ${results.database ? '✅ PASS' : '❌ FAIL'}`);

    const allPassed = Object.values(results).every(result => result);
    console.log(`\nOverall Status: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);

    if (!allPassed) {
      console.log('\n🔧 Recommended Actions:');
      if (!results.webhook) {
        console.log('- Check webhook configuration and network connectivity');
        console.log('- Verify WEBHOOK_SECRET is properly set');
        console.log('- Check if webhook endpoint is accessible from GitHub');
      }
      if (!results.deployment) {
        console.log('- Fix deployment script permissions and syntax');
        console.log('- Verify DEPLOY_SCRIPT path is correct');
      }
      if (!results.database) {
        console.log('- Initialize deployment database tables');
        console.log('- Check database connection and permissions');
      }
    }

    return results;
  }
}

// Run tests if called directly
if (require.main === module) {
  const tester = new WebhookTester();
  tester.runAllTests().catch(console.error);
}

module.exports = WebhookTester;
