#!/bin/bash

# StreamDB Online - Comprehensive Codebase Analysis & Fix
# This script analyzes and fixes code conflicts, duplicates, and issues

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

# Function to analyze codebase structure
analyze_codebase_structure() {
    log_info "🔍 Analyzing Codebase Structure"
    echo "==============================="
    
    local website_dir="/var/www/streamdb_onl_usr/data/www/streamdb.online"
    
    if [ ! -d "$website_dir" ]; then
        log_error "Website directory not found: $website_dir"
        return 1
    fi
    
    cd "$website_dir"
    
    log_info "Directory structure:"
    find . -type d -name "node_modules" -prune -o -type d -print | head -20
    
    echo ""
    log_info "Main files:"
    ls -la | grep -E "\.(js|json|html|css|tsx?|jsx?)$" | head -10
    
    echo ""
    log_info "Package files:"
    find . -name "package.json" -not -path "*/node_modules/*" | head -5
}

# Function to find duplicate files
find_duplicate_files() {
    log_info "🔍 Finding Duplicate Files"
    echo "=========================="
    
    local website_dir="/var/www/streamdb_onl_usr/data/www/streamdb.online"
    cd "$website_dir"
    
    # Find duplicate JavaScript files
    log_info "Checking for duplicate JavaScript files:"
    find . -name "*.js" -not -path "*/node_modules/*" -not -path "*/dist/*" | sort | uniq -d | head -10
    
    # Find duplicate TypeScript files
    log_info "Checking for duplicate TypeScript files:"
    find . -name "*.ts" -o -name "*.tsx" -not -path "*/node_modules/*" -not -path "*/dist/*" | sort | uniq -d | head -10
    
    # Find duplicate CSS files
    log_info "Checking for duplicate CSS files:"
    find . -name "*.css" -not -path "*/node_modules/*" -not -path "*/dist/*" | sort | uniq -d | head -10
    
    # Find duplicate configuration files
    log_info "Checking for duplicate config files:"
    find . -name "*.json" -o -name "*.yml" -o -name "*.yaml" -not -path "*/node_modules/*" | sort | uniq -d | head -10
}

# Function to check for code conflicts
check_code_conflicts() {
    log_info "🔍 Checking for Code Conflicts"
    echo "=============================="
    
    local website_dir="/var/www/streamdb_onl_usr/data/www/streamdb.online"
    cd "$website_dir"
    
    # Check for Git conflict markers
    log_info "Checking for Git conflict markers:"
    if find . -name "*.js" -o -name "*.ts" -o -name "*.tsx" -o -name "*.css" -o -name "*.html" | xargs grep -l "<<<<<<< HEAD\|=======" 2>/dev/null; then
        log_warning "Found Git conflict markers in files"
    else
        log_success "No Git conflict markers found"
    fi
    
    # Check for multiple package.json files
    log_info "Checking for multiple package.json files:"
    local package_files=$(find . -name "package.json" -not -path "*/node_modules/*" | wc -l)
    if [ "$package_files" -gt 1 ]; then
        log_warning "Found $package_files package.json files:"
        find . -name "package.json" -not -path "*/node_modules/*"
    else
        log_success "Single package.json found"
    fi
    
    # Check for multiple .env files
    log_info "Checking for multiple .env files:"
    local env_files=$(find . -name ".env*" -not -path "*/node_modules/*" | wc -l)
    if [ "$env_files" -gt 2 ]; then
        log_warning "Found $env_files .env files:"
        find . -name ".env*" -not -path "*/node_modules/*"
    else
        log_success "Appropriate number of .env files found"
    fi
}

# Function to validate database connections
validate_database_connections() {
    log_info "🔍 Validating Database Connections"
    echo "=================================="
    
    local website_dir="/var/www/streamdb_onl_usr/data/www/streamdb.online"
    
    # Check MySQL service
    if systemctl is-active --quiet mysql; then
        log_success "MySQL service is running"
        
        # Test MySQL connection
        if mysql -u root -e "SELECT 1;" >/dev/null 2>&1; then
            log_success "MySQL root connection works"
        else
            log_warning "MySQL root connection failed"
        fi
        
        # Check for StreamDB database
        if mysql -u root -e "USE streamdb_online; SELECT 1;" >/dev/null 2>&1; then
            log_success "StreamDB database exists"
        else
            log_warning "StreamDB database not found"
        fi
    else
        log_error "MySQL service is not running"
    fi
    
    # Check database configuration in code
    if [ -f "$website_dir/server/.env" ]; then
        log_info "Checking database configuration in server/.env:"
        grep -E "DB_|DATABASE_" "$website_dir/server/.env" | sed 's/=.*/=***/' || log_warning "No database config found"
    fi
    
    if [ -f "$website_dir/.env" ]; then
        log_info "Checking database configuration in .env:"
        grep -E "DB_|DATABASE_" "$website_dir/.env" | sed 's/=.*/=***/' || log_warning "No database config found"
    fi
}

# Function to check Node.js and PM2 status
check_nodejs_pm2() {
    log_info "🔍 Checking Node.js and PM2 Status"
    echo "==================================="
    
    # Check Node.js version
    if command -v node >/dev/null 2>&1; then
        log_success "Node.js version: $(node --version)"
    else
        log_error "Node.js not installed"
    fi
    
    # Check npm version
    if command -v npm >/dev/null 2>&1; then
        log_success "npm version: $(npm --version)"
    else
        log_error "npm not installed"
    fi
    
    # Check PM2 status
    if command -v pm2 >/dev/null 2>&1; then
        log_success "PM2 installed: $(pm2 --version)"
        
        log_info "PM2 processes:"
        pm2 status || log_warning "PM2 status check failed"
    else
        log_warning "PM2 not installed"
    fi
}

# Function to validate website files
validate_website_files() {
    log_info "🔍 Validating Website Files"
    echo "==========================="
    
    local website_dir="/var/www/streamdb_onl_usr/data/www/streamdb.online"
    cd "$website_dir"
    
    # Check for essential files
    local essential_files=(
        "package.json"
        "server/index.js"
        "dist/index.html"
        ".env"
        "server/.env"
    )
    
    for file in "${essential_files[@]}"; do
        if [ -f "$file" ]; then
            log_success "Found: $file"
        else
            log_warning "Missing: $file"
        fi
    done
    
    # Check dist directory
    if [ -d "dist" ]; then
        log_success "dist directory exists"
        log_info "dist contents:"
        ls -la dist/ | head -10
    else
        log_warning "dist directory missing - website may not be built"
    fi
    
    # Check server directory
    if [ -d "server" ]; then
        log_success "server directory exists"
        log_info "server contents:"
        ls -la server/ | head -10
    else
        log_error "server directory missing"
    fi
}

# Function to fix common issues
fix_common_issues() {
    log_info "🔧 Fixing Common Issues"
    echo "======================="
    
    local website_dir="/var/www/streamdb_onl_usr/data/www/streamdb.online"
    cd "$website_dir"
    
    # Fix file permissions
    log_info "Fixing file permissions..."
    chown -R www-data:www-data "$website_dir"
    chmod -R 755 "$website_dir"
    chmod -R 644 "$website_dir"/*.json 2>/dev/null || true
    chmod -R 600 "$website_dir"/.env* 2>/dev/null || true
    log_success "File permissions fixed"
    
    # Install dependencies if package.json exists
    if [ -f "package.json" ]; then
        log_info "Installing/updating dependencies..."
        npm install --production || log_warning "npm install failed"
    fi
    
    # Build project if needed
    if [ -f "package.json" ] && [ ! -d "dist" ]; then
        log_info "Building project..."
        npm run build || log_warning "Build failed"
    fi
    
    # Restart PM2 processes
    if command -v pm2 >/dev/null 2>&1; then
        log_info "Restarting PM2 processes..."
        pm2 restart all || log_warning "PM2 restart failed"
    fi
}

# Function to create system health check
create_health_check() {
    log_info "📝 Creating System Health Check"
    echo "==============================="
    
    cat > /usr/local/bin/streamdb-health-check.sh << 'EOF'
#!/bin/bash

# StreamDB Health Check Script

echo "=== StreamDB System Health Check ==="
echo "Date: $(date)"
echo ""

# Check MySQL
echo "MySQL Status:"
systemctl is-active mysql && echo "✅ MySQL: Running" || echo "❌ MySQL: Not Running"

# Check Nginx
echo "Nginx Status:"
systemctl is-active nginx && echo "✅ Nginx: Running" || echo "❌ Nginx: Not Running"

# Check FastPanel
echo "FastPanel Status:"
if pgrep -f fastpanel >/dev/null; then
    echo "✅ FastPanel: Running"
else
    echo "❌ FastPanel: Not Running"
fi

# Check Socat
echo "Socat Proxy Status:"
if pgrep socat >/dev/null; then
    echo "✅ Socat: Running"
else
    echo "❌ Socat: Not Running"
fi

# Check PM2
echo "PM2 Status:"
if command -v pm2 >/dev/null && pm2 list | grep -q "online"; then
    echo "✅ PM2: Running with processes"
else
    echo "❌ PM2: No processes or not running"
fi

# Check ports
echo ""
echo "Port Status:"
netstat -tlnp | grep -E "(80|443|3001|5501|5502|3306)" | while read line; do
    echo "✅ $line"
done

echo ""
echo "=== Health Check Complete ==="
EOF
    
    chmod +x /usr/local/bin/streamdb-health-check.sh
    log_success "Health check script created: /usr/local/bin/streamdb-health-check.sh"
}

# Main execution
main() {
    log_info "🔧 StreamDB Codebase Analysis & Fix"
    log_info "==================================="
    
    analyze_codebase_structure
    echo ""
    find_duplicate_files
    echo ""
    check_code_conflicts
    echo ""
    validate_database_connections
    echo ""
    check_nodejs_pm2
    echo ""
    validate_website_files
    echo ""
    fix_common_issues
    echo ""
    create_health_check
    
    echo ""
    log_success "🎉 Codebase Analysis & Fix Completed!"
    log_info ""
    log_info "📋 Summary:"
    log_info "   ✅ Codebase structure analyzed"
    log_info "   ✅ Duplicate files checked"
    log_info "   ✅ Code conflicts verified"
    log_info "   ✅ Database connections validated"
    log_info "   ✅ Node.js/PM2 status checked"
    log_info "   ✅ Website files validated"
    log_info "   ✅ Common issues fixed"
    log_info "   ✅ Health check script created"
    
    echo ""
    log_info "🔧 Run health check anytime with:"
    log_info "   /usr/local/bin/streamdb-health-check.sh"
}

# Run main function
main "$@"
