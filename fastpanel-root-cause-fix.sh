#!/bin/bash

# StreamDB Online - FastPanel Root Cause Analysis & Fix
# This script will identify and fix the ACTUAL problem with FastPanel

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to log messages
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "This script must be run as root (use sudo)"
        exit 1
    fi
}

# Function to analyze the ACTUAL FastPanel installation
analyze_fastpanel_installation() {
    log_info "🔍 ROOT CAUSE ANALYSIS: FastPanel Installation"
    echo "=================================================="
    
    # 1. Find the ACTUAL FastPanel binary location
    log_info "1. Locating FastPanel binary..."
    local fastpanel_binary=""
    
    # Check the process that was running
    if ps aux | grep -v grep | grep fastpanel; then
        log_info "Found running FastPanel processes:"
        ps aux | grep -v grep | grep fastpanel
        
        # Extract the actual binary path from running process
        fastpanel_binary=$(ps aux | grep -v grep | grep fastpanel | head -1 | awk '{for(i=11;i<=NF;i++) printf "%s ", $i; print ""}' | awk '{print $1}')
        log_success "FastPanel binary detected from process: $fastpanel_binary"
    fi
    
    # 2. Check all possible FastPanel locations
    log_info "2. Checking all possible FastPanel locations..."
    local possible_paths=(
        "/usr/local/fastpanel2/fastpanel"
        "/usr/local/fastpanel/fastpanel"
        "/usr/local/bin/fastpanel"
        "/usr/bin/fastpanel"
        "/opt/fastpanel/bin/fastpanel"
        "/usr/sbin/fastpanel"
    )
    
    for path in "${possible_paths[@]}"; do
        if [ -x "$path" ]; then
            log_success "Found executable: $path"
            ls -la "$path"
            
            # Check version and help
            echo "  Version info:"
            "$path" --version 2>/dev/null || "$path" -v 2>/dev/null || echo "    No version info available"
            
            # Check help for proper syntax
            echo "  Help info:"
            "$path" --help 2>/dev/null | head -10 || "$path" -h 2>/dev/null | head -10 || echo "    No help available"
            
            if [ -z "$fastpanel_binary" ]; then
                fastpanel_binary="$path"
            fi
        fi
    done
    
    # 3. Check FastPanel configuration files
    log_info "3. Checking FastPanel configuration..."
    local config_paths=(
        "/etc/fastpanel2/"
        "/etc/fastpanel/"
        "/usr/local/fastpanel2/etc/"
        "/usr/local/fastpanel/etc/"
        "/opt/fastpanel/etc/"
    )
    
    for config_dir in "${config_paths[@]}"; do
        if [ -d "$config_dir" ]; then
            log_success "Found config directory: $config_dir"
            ls -la "$config_dir" 2>/dev/null || true
            
            # Look for config files
            find "$config_dir" -name "*.conf" -o -name "*.cfg" -o -name "*.ini" 2>/dev/null | while read config_file; do
                log_info "Config file: $config_file"
                if [ -f "$config_file" ]; then
                    echo "  Content preview:"
                    head -20 "$config_file" | sed 's/^/    /'
                fi
            done
        fi
    done
    
    # 4. Check systemd services
    log_info "4. Checking systemd services..."
    local service_names=("fastpanel" "fastpanel2" "fp2")
    
    for service in "${service_names[@]}"; do
        if systemctl list-unit-files | grep -q "$service"; then
            log_success "Found systemd service: $service"
            systemctl status "$service" --no-pager || true
            
            # Show service file
            local service_file="/etc/systemd/system/${service}.service"
            if [ -f "$service_file" ]; then
                log_info "Service file: $service_file"
                cat "$service_file" | sed 's/^/    /'
            fi
        fi
    done
    
    # 5. Check what's actually listening on port 5501
    log_info "5. Checking port 5501 usage..."
    netstat -tlnp | grep ":5501" || log_warning "Nothing listening on port 5501"
    
    # 6. Check for any FastPanel logs
    log_info "6. Checking FastPanel logs..."
    local log_paths=(
        "/var/log/fastpanel/"
        "/var/log/fastpanel2/"
        "/usr/local/fastpanel2/logs/"
        "/tmp/fastpanel.log"
    )
    
    for log_dir in "${log_paths[@]}"; do
        if [ -d "$log_dir" ] || [ -f "$log_dir" ]; then
            log_success "Found log location: $log_dir"
            if [ -d "$log_dir" ]; then
                ls -la "$log_dir"
                # Show recent log entries
                find "$log_dir" -name "*.log" -exec tail -10 {} \; 2>/dev/null || true
            else
                tail -10 "$log_dir" 2>/dev/null || true
            fi
        fi
    done
    
    echo ""
    log_info "📋 ANALYSIS SUMMARY"
    echo "==================="
    log_info "Primary FastPanel binary: ${fastpanel_binary:-'NOT FOUND'}"
    
    return 0
}

# Function to determine the correct FastPanel startup method
determine_correct_startup() {
    log_info "🎯 DETERMINING CORRECT STARTUP METHOD"
    echo "====================================="
    
    # Find the working FastPanel binary
    local fastpanel_binary=""
    local working_method=""
    
    # Method 1: Check what was actually running before
    log_info "Method 1: Analyzing previous running process..."
    if ps aux | grep -v grep | grep fastpanel | grep -q "start"; then
        local prev_command=$(ps aux | grep -v grep | grep fastpanel | head -1 | awk '{for(i=11;i<=NF;i++) printf "%s ", $i; print ""}')
        log_success "Previous command: $prev_command"
        fastpanel_binary=$(echo "$prev_command" | awk '{print $1}')
        working_method="$prev_command"
    fi
    
    # Method 2: Try the most likely binary
    if [ -z "$fastpanel_binary" ]; then
        log_info "Method 2: Testing most likely binary..."
        if [ -x "/usr/local/fastpanel2/fastpanel" ]; then
            fastpanel_binary="/usr/local/fastpanel2/fastpanel"
            log_success "Using: $fastpanel_binary"
        fi
    fi
    
    # Method 3: Test the binary with different startup methods
    if [ -n "$fastpanel_binary" ] && [ -x "$fastpanel_binary" ]; then
        log_info "Method 3: Testing startup methods for $fastpanel_binary..."
        
        # Test different startup syntaxes
        local test_methods=(
            "$fastpanel_binary start"
            "$fastpanel_binary --start"
            "$fastpanel_binary daemon"
            "$fastpanel_binary --daemon"
            "$fastpanel_binary run"
            "$fastpanel_binary --bind 0.0.0.0 --port 5501"
            "$fastpanel_binary start --bind 0.0.0.0 --port 5501"
        )
        
        for method in "${test_methods[@]}"; do
            log_info "Testing: $method"
            
            # Try the method in background
            timeout 10s bash -c "$method &" 2>/dev/null || true
            sleep 2
            
            # Check if it's now listening
            if netstat -tlnp | grep -q ":5501"; then
                log_success "SUCCESS! Working method: $method"
                working_method="$method"
                
                # Kill the test process
                pkill -f fastpanel 2>/dev/null || true
                sleep 1
                break
            else
                log_warning "Failed: $method"
                # Clean up any failed processes
                pkill -f fastpanel 2>/dev/null || true
                sleep 1
            fi
        done
    fi
    
    echo ""
    log_info "📋 STARTUP METHOD DETERMINATION"
    echo "==============================="
    log_info "FastPanel binary: ${fastpanel_binary:-'NOT FOUND'}"
    log_info "Working method: ${working_method:-'NOT DETERMINED'}"
    
    # Export for use in other functions
    export FASTPANEL_BINARY="$fastpanel_binary"
    export FASTPANEL_METHOD="$working_method"
    
    if [ -n "$working_method" ]; then
        return 0
    else
        return 1
    fi
}

# Function to start FastPanel with the correct method
start_fastpanel_correctly() {
    log_info "🚀 STARTING FASTPANEL WITH CORRECT METHOD"
    echo "=========================================="
    
    if [ -z "$FASTPANEL_METHOD" ]; then
        log_error "No working startup method determined!"
        return 1
    fi
    
    log_info "Using method: $FASTPANEL_METHOD"
    
    # Start FastPanel
    log_info "Starting FastPanel..."
    eval "$FASTPANEL_METHOD &"
    
    # Wait for startup
    local wait_time=0
    local max_wait=30
    
    while [ $wait_time -lt $max_wait ]; do
        if netstat -tlnp | grep -q ":5501"; then
            log_success "FastPanel started successfully!"
            netstat -tlnp | grep ":5501"
            return 0
        fi
        
        sleep 2
        wait_time=$((wait_time + 2))
        log_info "Waiting for FastPanel to start... (${wait_time}s/${max_wait}s)"
    done
    
    log_error "FastPanel failed to start within ${max_wait} seconds"
    return 1
}

# Function to create proper startup configuration
create_proper_startup() {
    log_info "📝 CREATING PROPER STARTUP CONFIGURATION"
    echo "========================================"
    
    if [ -z "$FASTPANEL_METHOD" ]; then
        log_error "No working method to configure!"
        return 1
    fi
    
    # Create systemd service
    log_info "Creating systemd service..."
    cat > /etc/systemd/system/fastpanel-streamdb.service << EOF
[Unit]
Description=FastPanel for StreamDB
After=network.target

[Service]
Type=forking
User=root
ExecStart=$FASTPANEL_METHOD
ExecStop=/usr/bin/pkill -f fastpanel
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF
    
    systemctl daemon-reload
    systemctl enable fastpanel-streamdb
    log_success "Systemd service created and enabled"
    
    # Create startup script
    log_info "Creating startup script..."
    cat > /usr/local/bin/start-fastpanel-streamdb.sh << EOF
#!/bin/bash
# FastPanel startup script for StreamDB

# Check if already running
if netstat -tlnp | grep -q ":5501"; then
    echo "FastPanel is already running"
    exit 0
fi

# Start FastPanel
$FASTPANEL_METHOD &

# Wait for startup
sleep 5

# Verify
if netstat -tlnp | grep -q ":5501"; then
    echo "FastPanel started successfully"
    exit 0
else
    echo "FastPanel failed to start"
    exit 1
fi
EOF
    
    chmod +x /usr/local/bin/start-fastpanel-streamdb.sh
    log_success "Startup script created: /usr/local/bin/start-fastpanel-streamdb.sh"
}

# Function to test final configuration
test_final_configuration() {
    log_info "🧪 TESTING FINAL CONFIGURATION"
    echo "=============================="
    
    # Test local access
    log_info "Testing localhost access..."
    if curl -s --connect-timeout 5 http://127.0.0.1:5501 >/dev/null 2>&1; then
        log_success "Localhost access: Working"
    else
        log_error "Localhost access: Failed"
    fi
    
    # Test backend IP access
    local backend_ip=$(hostname -I | awk '{print $1}')
    log_info "Testing backend IP access (${backend_ip}:5501)..."
    if curl -s --connect-timeout 5 http://${backend_ip}:5501 >/dev/null 2>&1; then
        log_success "Backend IP access: Working"
    else
        log_error "Backend IP access: Failed"
    fi
    
    # Show process info
    log_info "FastPanel process info:"
    ps aux | grep -v grep | grep fastpanel || log_warning "No FastPanel processes found"
    
    # Show port info
    log_info "Port 5501 status:"
    netstat -tlnp | grep ":5501" || log_warning "Port 5501 not listening"
}

# Main execution
main() {
    log_info "🔧 FastPanel Root Cause Analysis & Fix"
    log_info "======================================"
    
    # Check prerequisites
    check_root
    
    # Stop any existing broken processes
    log_info "Stopping any existing FastPanel processes..."
    pkill -f fastpanel 2>/dev/null || true
    sleep 3
    
    # Execute root cause analysis
    analyze_fastpanel_installation
    echo ""
    
    # Determine correct startup method
    if determine_correct_startup; then
        echo ""
        start_fastpanel_correctly
        echo ""
        create_proper_startup
        echo ""
        test_final_configuration
        
        echo ""
        log_success "🎉 FastPanel root cause fixed!"
        log_info ""
        log_info "📋 SUMMARY:"
        log_info "   ✅ FastPanel binary: $FASTPANEL_BINARY"
        log_info "   ✅ Working method: $FASTPANEL_METHOD"
        log_info "   ✅ Systemd service: fastpanel-streamdb"
        log_info "   ✅ Startup script: /usr/local/bin/start-fastpanel-streamdb.sh"
        
        echo ""
        log_info "🌐 Test external access:"
        log_info "   https://fastpanel.streamdb.online/"
        
    else
        echo ""
        log_error "❌ Could not determine working FastPanel startup method!"
        log_info ""
        log_info "🔍 Manual investigation needed:"
        log_info "   1. Check FastPanel installation documentation"
        log_info "   2. Verify FastPanel is properly installed"
        log_info "   3. Check for FastPanel-specific configuration requirements"
    fi
}

# Run main function
main "$@"
