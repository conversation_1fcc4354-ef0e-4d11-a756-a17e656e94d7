import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useToast } from '@/hooks/use-toast';
import { 
  GitBranch, 
  Rocket, 
  Activity, 
  FileText, 
  CheckCircle, 
  XCircle, 
  Clock, 
  AlertTriangle,
  RefreshCw,
  Play,
  Eye,
  Wifi,
  Database,
  Settings
} from 'lucide-react';

interface DeploymentStatus {
  webhook_url: string;
  deployments: Array<{
    id: number;
    status: string;
    branch: string;
    commits: number;
    pusher: string;
    created_at: string;
    completed_at?: string;
    error_message?: string;
  }>;
  statistics: {
    total_deployments: number;
    successful_deployments: number;
    failed_deployments: number;
    running_deployments: number;
    avg_duration_seconds: number;
    last_deployment: string;
  };
  recent_logs: Array<{
    id: number;
    level: string;
    message: string;
    created_at: string;
    data?: any;
  }>;
  configuration: {
    allowed_branch: string;
    max_deployments_per_hour: number;
    webhook_secret_configured: boolean;
  };
}

interface WebhookTest {
  webhook_url: string;
  webhook_secret_configured: boolean;
  deploy_script_exists: boolean;
  deploy_script_path: string;
  database_connected: boolean;
  allowed_branch: string;
  rate_limit: string;
  test_timestamp: string;
}

const GitHubDeployment: React.FC = () => {
  const [status, setStatus] = useState<DeploymentStatus | null>(null);
  const [webhookTest, setWebhookTest] = useState<WebhookTest | null>(null);
  const [loading, setLoading] = useState(false);
  const [deploying, setDeploying] = useState(false);
  const { toast } = useToast();

  const fetchStatus = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/webhook/status');
      if (!response.ok) throw new Error('Failed to fetch status');
      const data = await response.json();
      setStatus(data);
    } catch (error) {
      console.error('Error fetching deployment status:', error);
      toast({
        title: "Error",
        description: "Failed to fetch deployment status",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const testWebhook = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/webhook/test');
      if (!response.ok) throw new Error('Failed to test webhook');
      const data = await response.json();
      setWebhookTest(data);
      toast({
        title: "Webhook Test Complete",
        description: "Check the results below",
      });
    } catch (error) {
      console.error('Error testing webhook:', error);
      toast({
        title: "Test Failed",
        description: "Failed to test webhook connection",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const triggerManualDeploy = async () => {
    try {
      setDeploying(true);
      const response = await fetch('/api/webhook/deploy', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ branch: 'main' }),
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Deployment failed');
      }
      
      const result = await response.json();
      toast({
        title: "Deployment Started",
        description: "Manual deployment completed successfully",
      });
      
      // Refresh status after deployment
      setTimeout(fetchStatus, 2000);
      
    } catch (error) {
      console.error('Error triggering deployment:', error);
      toast({
        title: "Deployment Failed",
        description: error instanceof Error ? error.message : "Failed to trigger deployment",
        variant: "destructive",
      });
    } finally {
      setDeploying(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'SUCCESS':
        return <Badge className="bg-green-600 hover:bg-green-700"><CheckCircle className="w-3 h-3 mr-1" />Success</Badge>;
      case 'FAILED':
        return <Badge className="bg-red-600 hover:bg-red-700"><XCircle className="w-3 h-3 mr-1" />Failed</Badge>;
      case 'STARTED':
      case 'RUNNING':
        return <Badge className="bg-blue-600 hover:bg-blue-700"><Clock className="w-3 h-3 mr-1" />Running</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const getLevelBadge = (level: string) => {
    switch (level) {
      case 'ERROR':
        return <Badge className="bg-red-600 hover:bg-red-700">Error</Badge>;
      case 'WARNING':
        return <Badge className="bg-yellow-600 hover:bg-yellow-700">Warning</Badge>;
      case 'SUCCESS':
        return <Badge className="bg-green-600 hover:bg-green-700">Success</Badge>;
      case 'INFO':
        return <Badge className="bg-blue-600 hover:bg-blue-700">Info</Badge>;
      default:
        return <Badge variant="secondary">{level}</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const formatDuration = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  useEffect(() => {
    fetchStatus();
  }, []);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-primary">GitHub Deployment</h2>
          <p className="text-muted-foreground">Manage automatic deployments from GitHub</p>
        </div>
        <Button 
          onClick={fetchStatus} 
          disabled={loading}
          variant="outline"
          size="sm"
        >
          <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Action Buttons */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Button 
          onClick={testWebhook} 
          disabled={loading}
          className="h-auto p-4 flex flex-col items-center space-y-2"
          variant="outline"
        >
          <Wifi className="w-6 h-6" />
          <span>Test Webhook</span>
        </Button>
        
        <Button 
          onClick={fetchStatus} 
          disabled={loading}
          className="h-auto p-4 flex flex-col items-center space-y-2"
          variant="outline"
        >
          <Activity className="w-6 h-6" />
          <span>Check Status</span>
        </Button>
        
        <Button 
          onClick={triggerManualDeploy} 
          disabled={deploying || loading}
          className="h-auto p-4 flex flex-col items-center space-y-2"
          variant="outline"
        >
          <Play className={`w-6 h-6 ${deploying ? 'animate-pulse' : ''}`} />
          <span>Manual Deploy</span>
        </Button>
        
        <Button 
          onClick={() => window.open('/api/webhook/logs', '_blank')} 
          className="h-auto p-4 flex flex-col items-center space-y-2"
          variant="outline"
        >
          <FileText className="w-6 h-6" />
          <span>View Logs</span>
        </Button>
      </div>

      <Tabs defaultValue="status" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="status">Status</TabsTrigger>
          <TabsTrigger value="deployments">Deployments</TabsTrigger>
          <TabsTrigger value="logs">Logs</TabsTrigger>
          <TabsTrigger value="config">Configuration</TabsTrigger>
        </TabsList>

        <TabsContent value="status" className="space-y-4">
          {webhookTest && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Wifi className="w-5 h-5 mr-2" />
                  Webhook Test Results
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center justify-between">
                    <span>Webhook URL:</span>
                    <code className="text-sm bg-muted px-2 py-1 rounded">{webhookTest.webhook_url}</code>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Secret Configured:</span>
                    {webhookTest.webhook_secret_configured ? 
                      <Badge className="bg-green-600">Yes</Badge> : 
                      <Badge className="bg-red-600">No</Badge>
                    }
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Deploy Script:</span>
                    {webhookTest.deploy_script_exists ? 
                      <Badge className="bg-green-600">Found</Badge> : 
                      <Badge className="bg-red-600">Missing</Badge>
                    }
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Database:</span>
                    {webhookTest.database_connected ? 
                      <Badge className="bg-green-600">Connected</Badge> : 
                      <Badge className="bg-red-600">Disconnected</Badge>
                    }
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {status && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Statistics</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span>Total Deployments:</span>
                    <span className="font-semibold">{status.statistics.total_deployments || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Successful:</span>
                    <span className="font-semibold text-green-600">{status.statistics.successful_deployments || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Failed:</span>
                    <span className="font-semibold text-red-600">{status.statistics.failed_deployments || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Running:</span>
                    <span className="font-semibold text-blue-600">{status.statistics.running_deployments || 0}</span>
                  </div>
                  {status.statistics.avg_duration_seconds && (
                    <div className="flex justify-between">
                      <span>Avg Duration:</span>
                      <span className="font-semibold">{formatDuration(status.statistics.avg_duration_seconds)}</span>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Configuration</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span>Branch:</span>
                    <Badge variant="outline">{status.configuration.allowed_branch}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Rate Limit:</span>
                    <span className="text-sm">{status.configuration.max_deployments_per_hour}/hour</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Webhook Secret:</span>
                    {status.configuration.webhook_secret_configured ? 
                      <Badge className="bg-green-600">Configured</Badge> : 
                      <Badge className="bg-red-600">Missing</Badge>
                    }
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Webhook URL</CardTitle>
                </CardHeader>
                <CardContent>
                  <code className="text-xs bg-muted p-2 rounded block break-all">
                    {status.webhook_url}
                  </code>
                  <p className="text-xs text-muted-foreground mt-2">
                    Use this URL in your GitHub webhook configuration
                  </p>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        <TabsContent value="deployments" className="space-y-4">
          {status?.deployments && status.deployments.length > 0 ? (
            <Card>
              <CardHeader>
                <CardTitle>Recent Deployments</CardTitle>
                <CardDescription>Last 10 deployment attempts</CardDescription>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-96">
                  <div className="space-y-3">
                    {status.deployments.map((deployment) => (
                      <div key={deployment.id} className="border rounded-lg p-3 space-y-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            {getStatusBadge(deployment.status)}
                            <Badge variant="outline">
                              <GitBranch className="w-3 h-3 mr-1" />
                              {deployment.branch}
                            </Badge>
                            <span className="text-sm text-muted-foreground">
                              {deployment.commits} commits
                            </span>
                          </div>
                          <span className="text-xs text-muted-foreground">
                            {formatDate(deployment.created_at)}
                          </span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span>Pusher: {deployment.pusher}</span>
                          {deployment.completed_at && (
                            <span>
                              Duration: {formatDuration(
                                Math.floor((new Date(deployment.completed_at).getTime() - new Date(deployment.created_at).getTime()) / 1000)
                              )}
                            </span>
                          )}
                        </div>
                        {deployment.error_message && (
                          <div className="text-sm text-red-600 bg-red-50 dark:bg-red-900/20 p-2 rounded">
                            {deployment.error_message}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="text-center py-8">
                <Rocket className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
                <p className="text-muted-foreground">No deployments found</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="logs" className="space-y-4">
          {status?.recent_logs && status.recent_logs.length > 0 ? (
            <Card>
              <CardHeader>
                <CardTitle>Recent Logs</CardTitle>
                <CardDescription>Last 20 deployment log entries</CardDescription>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-96">
                  <div className="space-y-2">
                    {status.recent_logs.map((log) => (
                      <div key={log.id} className="border rounded p-2 space-y-1">
                        <div className="flex items-center justify-between">
                          {getLevelBadge(log.level)}
                          <span className="text-xs text-muted-foreground">
                            {formatDate(log.created_at)}
                          </span>
                        </div>
                        <p className="text-sm">{log.message}</p>
                        {log.data && (
                          <details className="text-xs">
                            <summary className="cursor-pointer text-muted-foreground">Details</summary>
                            <pre className="mt-1 bg-muted p-2 rounded overflow-x-auto">
                              {JSON.stringify(log.data, null, 2)}
                            </pre>
                          </details>
                        )}
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="text-center py-8">
                <FileText className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
                <p className="text-muted-foreground">No logs found</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="config" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Settings className="w-5 h-5 mr-2" />
                GitHub Webhook Configuration
              </CardTitle>
              <CardDescription>
                Follow these steps to configure your GitHub repository webhook
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div>
                  <h4 className="font-semibold mb-2">1. Go to your GitHub repository</h4>
                  <p className="text-sm text-muted-foreground">Navigate to Settings → Webhooks → Add webhook</p>
                </div>
                
                <div>
                  <h4 className="font-semibold mb-2">2. Configure webhook settings</h4>
                  <div className="space-y-2 text-sm">
                    <div>
                      <strong>Payload URL:</strong>
                      <code className="ml-2 bg-muted px-2 py-1 rounded">
                        {status?.webhook_url || 'https://streamdb.online/api/webhook/github'}
                      </code>
                    </div>
                    <div>
                      <strong>Content type:</strong>
                      <code className="ml-2 bg-muted px-2 py-1 rounded">application/json</code>
                    </div>
                    <div>
                      <strong>Secret:</strong>
                      <span className="ml-2 text-muted-foreground">Use WEBHOOK_SECRET from your .env file</span>
                    </div>
                    <div>
                      <strong>Events:</strong>
                      <span className="ml-2 text-muted-foreground">Just the push event</span>
                    </div>
                    <div>
                      <strong>Active:</strong>
                      <span className="ml-2 text-muted-foreground">✅ Checked</span>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-semibold mb-2">3. Test the webhook</h4>
                  <p className="text-sm text-muted-foreground">
                    After saving, GitHub will send a test payload. Check the "Recent Deliveries" tab to verify it worked.
                  </p>
                </div>
              </div>
              
              {status?.configuration && !status.configuration.webhook_secret_configured && (
                <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                  <div className="flex items-center">
                    <AlertTriangle className="w-5 h-5 text-yellow-600 mr-2" />
                    <span className="font-semibold text-yellow-800 dark:text-yellow-200">Warning</span>
                  </div>
                  <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
                    Webhook secret is not configured. Add WEBHOOK_SECRET to your .env file for security.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default GitHubDeployment;
