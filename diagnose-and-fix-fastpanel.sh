#!/bin/bash

# StreamDB Online - Comprehensive FastPanel Diagnosis and Fix
# This script diagnoses and fixes FastPanel access issues

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROXY_SERVER_IP="*************"
BACKEND_SERVER_IP="***********"
FASTPANEL_SUBDOMAIN="fastpanel.streamdb.online"

# Function to log messages
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "This script must be run as root (use sudo)"
        exit 1
    fi
}

# Function to diagnose current state
diagnose_current_state() {
    log_info "🔍 Diagnosing Current FastPanel State"
    echo "====================================="
    
    # Check what's listening on port 5501
    echo ""
    log_info "Port 5501 listeners:"
    netstat -tlnp | grep ":5501" || log_warning "Nothing listening on port 5501"
    
    # Check FastPanel process
    echo ""
    log_info "FastPanel processes:"
    ps aux | grep -i fastpanel | grep -v grep || log_warning "No FastPanel processes found"
    
    # Check FastPanel service status
    echo ""
    log_info "FastPanel service status:"
    systemctl status fastpanel 2>/dev/null || log_warning "FastPanel service not found or not running"
    
    # Check if FastPanel responds locally
    echo ""
    log_info "Testing local FastPanel access:"
    if curl -s --connect-timeout 5 http://127.0.0.1:5501 >/dev/null 2>&1; then
        log_success "FastPanel responds on localhost"
    else
        log_error "FastPanel NOT responding on localhost"
    fi
    
    # Check if FastPanel responds on backend IP
    echo ""
    log_info "Testing FastPanel on backend IP:"
    if curl -s --connect-timeout 5 http://${BACKEND_SERVER_IP}:5501 >/dev/null 2>&1; then
        log_success "FastPanel responds on backend IP"
    else
        log_error "FastPanel NOT responding on backend IP"
    fi
    
    # Check DNS resolution
    echo ""
    log_info "DNS resolution test:"
    if nslookup ${FASTPANEL_SUBDOMAIN} >/dev/null 2>&1; then
        log_success "Subdomain resolves correctly"
        nslookup ${FASTPANEL_SUBDOMAIN} | grep -A1 "Name:"
    else
        log_error "Subdomain does not resolve"
    fi
    
    # Check firewall rules
    echo ""
    log_info "Current firewall rules:"
    ufw status numbered | head -15
}

# Function to fix FastPanel listening configuration
fix_fastpanel_listening() {
    log_info "🔧 Fixing FastPanel Listening Configuration"
    echo "============================================="
    
    # Find and kill any existing FastPanel processes
    log_info "Stopping existing FastPanel processes..."
    pkill -f fastpanel 2>/dev/null || log_info "No FastPanel processes to stop"
    
    # Wait a moment
    sleep 2
    
    # Try to start FastPanel with correct configuration
    log_info "Starting FastPanel..."
    
    # Method 1: Try systemctl
    if systemctl start fastpanel 2>/dev/null; then
        log_success "FastPanel started via systemctl"
    else
        log_warning "Could not start FastPanel via systemctl"
        
        # Method 2: Try to find and run FastPanel binary directly
        log_info "Searching for FastPanel binary..."
        
        local fastpanel_binary=""
        local possible_paths=(
            "/usr/local/bin/fastpanel"
            "/usr/bin/fastpanel"
            "/opt/fastpanel/bin/fastpanel"
            "/usr/local/fastpanel/bin/fastpanel"
        )
        
        for path in "${possible_paths[@]}"; do
            if [ -x "$path" ]; then
                fastpanel_binary="$path"
                log_success "Found FastPanel binary: $path"
                break
            fi
        done
        
        if [ -n "$fastpanel_binary" ]; then
            log_info "Starting FastPanel manually..."
            nohup "$fastpanel_binary" --bind 0.0.0.0 --port 5501 >/dev/null 2>&1 &
            sleep 3
        else
            log_warning "FastPanel binary not found"
        fi
    fi
    
    # Verify FastPanel is now running
    sleep 3
    if netstat -tlnp | grep -q ":5501"; then
        log_success "FastPanel is now listening on port 5501"
        netstat -tlnp | grep ":5501"
    else
        log_error "FastPanel still not listening on port 5501"
    fi
}

# Function to create Nginx proxy for FastPanel
create_nginx_fastpanel_proxy() {
    log_info "🔧 Creating Nginx Proxy for FastPanel"
    echo "======================================"
    
    # Create FastPanel proxy configuration
    cat > /etc/nginx/conf.d/fastpanel-proxy.conf << 'EOF'
# FastPanel Proxy Configuration
server {
    listen 80;
    server_name fastpanel.streamdb.online;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    
    # Proxy to local FastPanel
    location / {
        proxy_pass http://127.0.0.1:5501;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # FastPanel specific headers
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Server $host;
        
        # Increase timeouts for FastPanel
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # Buffer settings
        proxy_buffering off;
        proxy_request_buffering off;
    }
    
    # Health check endpoint
    location /health {
        access_log off;
        return 200 "FastPanel Proxy OK\n";
        add_header Content-Type text/plain;
    }
}

# HTTPS redirect (if SSL is configured later)
server {
    listen 443 ssl http2;
    server_name fastpanel.streamdb.online;
    
    # Temporary redirect to HTTP (update when SSL is configured)
    return 301 http://$server_name$request_uri;
}
EOF

    # Test Nginx configuration
    if nginx -t 2>/dev/null; then
        log_success "Nginx configuration is valid"
        systemctl reload nginx
        log_success "Nginx reloaded with FastPanel proxy"
    else
        log_error "Nginx configuration test failed"
        log_info "Nginx error details:"
        nginx -t
    fi
}

# Function to update firewall for FastPanel access
update_firewall_for_fastpanel() {
    log_info "🔧 Updating Firewall for FastPanel Access"
    echo "=========================================="
    
    # Ensure proxy server can access FastPanel
    ufw allow from ${PROXY_SERVER_IP} to any port 5501 comment "FastPanel via proxy"
    ufw allow from ${PROXY_SERVER_IP} to any port 80 comment "HTTP via proxy"
    ufw allow from ${PROXY_SERVER_IP} to any port 443 comment "HTTPS via proxy"
    
    # Allow localhost access to FastPanel
    ufw allow from 127.0.0.1 to any port 5501 comment "FastPanel localhost"
    
    log_success "Firewall rules updated for FastPanel access"
    
    # Show current rules
    log_info "Current firewall status:"
    ufw status numbered | head -20
}

# Function to test connectivity
test_connectivity() {
    log_info "🔍 Testing Connectivity"
    echo "======================="
    
    # Test local FastPanel
    echo ""
    log_info "Testing local FastPanel (127.0.0.1:5501):"
    if curl -s --connect-timeout 5 -I http://127.0.0.1:5501 2>/dev/null; then
        log_success "Local FastPanel: Accessible"
    else
        log_error "Local FastPanel: Not accessible"
    fi
    
    # Test FastPanel on backend IP
    echo ""
    log_info "Testing FastPanel on backend IP (${BACKEND_SERVER_IP}:5501):"
    if curl -s --connect-timeout 5 -I http://${BACKEND_SERVER_IP}:5501 2>/dev/null; then
        log_success "Backend FastPanel: Accessible"
    else
        log_error "Backend FastPanel: Not accessible"
    fi
    
    # Test HTTP proxy
    echo ""
    log_info "Testing HTTP proxy (${BACKEND_SERVER_IP}:80):"
    if curl -s --connect-timeout 5 -H "Host: fastpanel.streamdb.online" http://${BACKEND_SERVER_IP}:80/health 2>/dev/null; then
        log_success "HTTP proxy: Working"
    else
        log_warning "HTTP proxy: May not be working"
    fi
    
    # Test main website
    echo ""
    log_info "Testing main website:"
    if curl -s --connect-timeout 10 https://streamdb.online >/dev/null 2>&1; then
        log_success "Main website: Accessible"
    else
        log_error "Main website: Not accessible"
    fi
}

# Function to show proxy server configuration
show_proxy_server_config() {
    log_info "📋 Proxy Server Configuration Required"
    echo "======================================"
    
    echo ""
    log_info "Add this to your proxy server (*************) Nginx configuration:"
    echo ""
    cat << 'EOF'
# FastPanel Subdomain Configuration
server {
    listen 80;
    listen 443 ssl http2;
    server_name fastpanel.streamdb.online;
    
    # SSL configuration (add your certificates)
    # ssl_certificate /path/to/ssl/cert.pem;
    # ssl_certificate_key /path/to/ssl/key.pem;
    
    # Proxy to backend server
    location / {
        proxy_pass http://***********:80;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host fastpanel.streamdb.online;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
EOF
    
    echo ""
    log_info "After adding this configuration:"
    log_info "1. Test config: nginx -t"
    log_info "2. Reload Nginx: systemctl reload nginx"
    log_info "3. Test access: https://fastpanel.streamdb.online/"
}

# Function to create comprehensive verification
create_comprehensive_verification() {
    cat > /root/test-fastpanel-access.sh << 'EOF'
#!/bin/bash

echo "🔍 Comprehensive FastPanel Access Test"
echo "======================================"

# Test all possible access methods
echo ""
echo "1. Testing local FastPanel access:"
curl -s --connect-timeout 5 -I http://127.0.0.1:5501 && echo "✅ Local access: OK" || echo "❌ Local access: FAILED"

echo ""
echo "2. Testing backend IP FastPanel access:"
curl -s --connect-timeout 5 -I http://***********:5501 && echo "✅ Backend IP access: OK" || echo "❌ Backend IP access: FAILED"

echo ""
echo "3. Testing HTTP proxy access:"
curl -s --connect-timeout 5 -H "Host: fastpanel.streamdb.online" http://***********:80/health && echo "✅ HTTP proxy: OK" || echo "❌ HTTP proxy: FAILED"

echo ""
echo "4. Testing subdomain access:"
curl -s --connect-timeout 10 https://fastpanel.streamdb.online/ && echo "✅ Subdomain HTTPS: OK" || echo "❌ Subdomain HTTPS: FAILED"
curl -s --connect-timeout 10 http://fastpanel.streamdb.online/ && echo "✅ Subdomain HTTP: OK" || echo "❌ Subdomain HTTP: FAILED"

echo ""
echo "5. Testing main website:"
curl -s --connect-timeout 10 https://streamdb.online && echo "✅ Main website: OK" || echo "❌ Main website: FAILED"

echo ""
echo "Current listening ports:"
netstat -tlnp | grep -E ":(80|443|5501|3001)" | head -10

echo ""
echo "🎯 If FastPanel subdomain still doesn't work:"
echo "   1. Check proxy server (*************) Nginx configuration"
echo "   2. Verify DNS points to proxy server, not backend"
echo "   3. Check proxy server firewall allows connections"
echo "   4. Test from external network, not localhost"
EOF

    chmod +x /root/test-fastpanel-access.sh
    log_success "Comprehensive test script created: /root/test-fastpanel-access.sh"
}

# Main execution
main() {
    log_info "🔧 Comprehensive FastPanel Diagnosis and Fix"
    log_info "============================================="
    
    # Check prerequisites
    check_root
    
    # Run diagnosis and fixes
    diagnose_current_state
    echo ""
    fix_fastpanel_listening
    echo ""
    create_nginx_fastpanel_proxy
    echo ""
    update_firewall_for_fastpanel
    echo ""
    test_connectivity
    echo ""
    create_comprehensive_verification
    echo ""
    show_proxy_server_config
    
    echo ""
    log_success "🎉 FastPanel diagnosis and fix completed!"
    log_info ""
    log_info "🔍 Next steps:"
    log_info "1. Configure proxy server Nginx (see configuration above)"
    log_info "2. Test access: https://fastpanel.streamdb.online/"
    log_info "3. Run comprehensive test: ./test-fastpanel-access.sh"
    log_info "4. If still not working, check proxy server configuration"
    log_info ""
    log_warning "⚠️  Remember: The subdomain must be configured on your proxy server!"
}

# Run main function
main "$@"
