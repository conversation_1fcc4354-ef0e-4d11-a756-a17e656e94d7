#!/usr/bin/env node

/**
 * StreamDB Online - Webhook Diagnostic Tool
 * 
 * This script diagnoses webhook-related issues and provides solutions
 */

const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '../.env') });

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  bright: '\x1b[1m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️ ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️ ${message}`, 'blue');
}

async function checkEnvironmentVariables() {
  log('\n🔍 Checking Environment Variables...', 'cyan');
  
  const requiredVars = [
    'DB_HOST', 'DB_USER', 'DB_PASSWORD', 'DB_NAME',
    'WEBHOOK_SECRET', 'JWT_SECRET', 'SESSION_SECRET'
  ];
  
  const missingVars = [];
  const presentVars = [];
  
  requiredVars.forEach(varName => {
    if (process.env[varName]) {
      presentVars.push(varName);
      logSuccess(`${varName}: Configured`);
    } else {
      missingVars.push(varName);
      logError(`${varName}: Missing`);
    }
  });
  
  if (missingVars.length > 0) {
    logError(`Missing ${missingVars.length} required environment variables`);
    return false;
  } else {
    logSuccess('All required environment variables are configured');
    return true;
  }
}

async function checkDatabaseConnection() {
  log('\n🔍 Checking Database Connection...', 'cyan');
  
  const dbConfig = {
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    charset: 'utf8mb4',
    timezone: '+00:00'
  };

  // Use socket for production
  if (process.env.NODE_ENV === 'production' && process.env.DB_SOCKET) {
    dbConfig.socketPath = process.env.DB_SOCKET;
    logInfo(`Using socket connection: ${process.env.DB_SOCKET}`);
  } else {
    dbConfig.host = process.env.DB_HOST || 'localhost';
    dbConfig.port = process.env.DB_PORT || 3306;
    logInfo(`Using TCP connection: ${dbConfig.host}:${dbConfig.port}`);
  }
  
  try {
    const connection = await mysql.createConnection(dbConfig);
    logSuccess('Database connection successful');
    
    // Test basic query
    const [rows] = await connection.execute('SELECT 1 as test');
    if (rows[0].test === 1) {
      logSuccess('Database queries working');
    }
    
    await connection.end();
    return true;
  } catch (error) {
    logError(`Database connection failed: ${error.message}`);
    
    if (error.code === 'ECONNREFUSED') {
      logWarning('MySQL server may not be running');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      logWarning('Database credentials may be incorrect');
    }
    
    return false;
  }
}

async function checkDeploymentTables() {
  log('\n🔍 Checking Deployment Tables...', 'cyan');
  
  const dbConfig = {
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    charset: 'utf8mb4'
  };

  if (process.env.NODE_ENV === 'production' && process.env.DB_SOCKET) {
    dbConfig.socketPath = process.env.DB_SOCKET;
  } else {
    dbConfig.host = process.env.DB_HOST || 'localhost';
    dbConfig.port = process.env.DB_PORT || 3306;
  }
  
  try {
    const connection = await mysql.createConnection(dbConfig);
    
    const requiredTables = ['deployments', 'deployment_logs', 'webhook_events', 'manual_deployments', 'deployment_config'];
    const missingTables = [];
    
    for (const tableName of requiredTables) {
      try {
        const [rows] = await connection.execute(`SELECT 1 FROM ${tableName} LIMIT 1`);
        logSuccess(`Table '${tableName}' exists and accessible`);
      } catch (error) {
        missingTables.push(tableName);
        logError(`Table '${tableName}' missing or inaccessible`);
      }
    }
    
    await connection.end();
    
    if (missingTables.length > 0) {
      logError(`Missing ${missingTables.length} deployment tables`);
      logInfo('Run: node server/scripts/init-deployment-db.js');
      return false;
    } else {
      logSuccess('All deployment tables exist');
      return true;
    }
    
  } catch (error) {
    logError(`Failed to check deployment tables: ${error.message}`);
    return false;
  }
}

async function checkWebhookEndpoint() {
  log('\n🔍 Checking Webhook Configuration...', 'cyan');
  
  // Check if webhook secret is configured
  if (process.env.WEBHOOK_SECRET) {
    logSuccess('Webhook secret is configured');
  } else {
    logError('Webhook secret is missing');
    return false;
  }
  
  // Check if deployment script exists
  const deployScript = process.env.DEPLOY_SCRIPT || path.join(__dirname, '../../deployment/deploy.sh');
  if (fs.existsSync(deployScript)) {
    logSuccess(`Deployment script exists: ${deployScript}`);
  } else {
    logWarning(`Deployment script not found: ${deployScript}`);
  }
  
  return true;
}

async function runDiagnostic() {
  log('🔍 StreamDB Online - Webhook Diagnostic Tool', 'bright');
  log('===============================================', 'bright');
  
  const checks = [
    { name: 'Environment Variables', fn: checkEnvironmentVariables },
    { name: 'Database Connection', fn: checkDatabaseConnection },
    { name: 'Deployment Tables', fn: checkDeploymentTables },
    { name: 'Webhook Configuration', fn: checkWebhookEndpoint }
  ];
  
  const results = [];
  
  for (const check of checks) {
    try {
      const result = await check.fn();
      results.push({ name: check.name, passed: result });
    } catch (error) {
      logError(`${check.name} check failed: ${error.message}`);
      results.push({ name: check.name, passed: false, error: error.message });
    }
  }
  
  // Summary
  log('\n📊 Diagnostic Summary', 'cyan');
  log('====================', 'cyan');
  
  const passed = results.filter(r => r.passed).length;
  const total = results.length;
  
  results.forEach(result => {
    if (result.passed) {
      logSuccess(`${result.name}: PASSED`);
    } else {
      logError(`${result.name}: FAILED`);
      if (result.error) {
        log(`   Error: ${result.error}`, 'red');
      }
    }
  });
  
  log(`\n📈 Overall: ${passed}/${total} checks passed`, passed === total ? 'green' : 'red');
  
  if (passed !== total) {
    log('\n🔧 Recommended Actions:', 'yellow');
    
    const failedChecks = results.filter(r => !r.passed);
    
    if (failedChecks.some(c => c.name === 'Environment Variables')) {
      log('  1. Check your .env file configuration', 'yellow');
    }
    
    if (failedChecks.some(c => c.name === 'Database Connection')) {
      log('  2. Verify MySQL server is running and credentials are correct', 'yellow');
    }
    
    if (failedChecks.some(c => c.name === 'Deployment Tables')) {
      log('  3. Run: node server/scripts/init-deployment-db.js', 'yellow');
    }
    
    if (failedChecks.some(c => c.name === 'Webhook Configuration')) {
      log('  4. Configure webhook secret in .env file', 'yellow');
    }
  } else {
    log('\n🎉 All checks passed! Your webhook system should be working correctly.', 'green');
  }
  
  return passed === total;
}

// Run diagnostic if called directly
if (require.main === module) {
  runDiagnostic()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      logError(`Diagnostic failed: ${error.message}`);
      process.exit(1);
    });
}

module.exports = { runDiagnostic };
