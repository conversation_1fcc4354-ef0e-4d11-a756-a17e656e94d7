#!/bin/bash

# StreamDB Online - FastPanel Subdomain Access Configuration
# This script configures FastPanel to work via https://fastpanel.streamdb.online/

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROXY_SERVER_IP="*************"
BACKEND_SERVER_IP="***********"
FASTPANEL_SUBDOMAIN="fastpanel.streamdb.online"

# Function to log messages
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "This script must be run as root (use sudo)"
        exit 1
    fi
}

# Function to configure secure firewall for subdomain access
configure_subdomain_firewall() {
    log_info "Configuring firewall for FastPanel subdomain access..."
    
    # Reset UFW to clean state
    log_info "Resetting UFW and configuring for subdomain access..."
    ufw --force reset
    
    # Set default policies
    ufw default deny incoming
    ufw default allow outgoing
    
    # CRITICAL: Allow SSH (don't lock yourself out!)
    log_info "Allowing SSH access on port 22..."
    ufw allow 22/tcp comment "SSH access"
    
    # Allow proxy server to access ALL web services (for subdomain routing)
    log_info "Allowing proxy server (${PROXY_SERVER_IP}) to access web services..."
    ufw allow from ${PROXY_SERVER_IP} to any port 80 comment "Proxy HTTP access"
    ufw allow from ${PROXY_SERVER_IP} to any port 443 comment "Proxy HTTPS access"
    ufw allow from ${PROXY_SERVER_IP} to any port 5501 comment "FastPanel via proxy"
    ufw allow from ${PROXY_SERVER_IP} to any port 8080 comment "FastPanel Nginx 8080"
    ufw allow from ${PROXY_SERVER_IP} to any port 7777 comment "FastPanel Nginx 7777"
    ufw allow from ${PROXY_SERVER_IP} to any port 8888 comment "FastPanel Nginx 8888"
    
    # Allow localhost connections (for internal services)
    log_info "Allowing localhost connections..."
    ufw allow from 127.0.0.1 comment "Localhost access"
    ufw allow from ::1 comment "IPv6 localhost access"
    
    # Node.js application - LOCALHOST AND PROXY ONLY
    log_info "Configuring Node.js application access..."
    ufw allow from 127.0.0.1 to any port 3001 comment "Node.js localhost"
    ufw allow from ${PROXY_SERVER_IP} to any port 3001 comment "Node.js via proxy"
    
    # Explicitly deny dangerous ports from external access (not from proxy)
    log_info "Blocking dangerous ports from external access..."
    ufw deny 3306 comment "Block external MySQL"
    ufw deny 9000 comment "Block webhook port"
    
    # Block direct FastPanel access from external (only allow via proxy)
    log_info "Blocking direct FastPanel access (only allow via proxy)..."
    ufw deny from any to any port 5501 comment "Block direct FastPanel"
    ufw allow from ${PROXY_SERVER_IP} to any port 5501 comment "FastPanel via proxy only"
    
    # Enable firewall
    log_info "Enabling UFW firewall..."
    ufw --force enable
    
    log_success "Firewall configured for subdomain access"
}

# Function to check FastPanel configuration
check_fastpanel_config() {
    log_info "Checking FastPanel configuration for subdomain access..."
    
    # Check if FastPanel is running
    if systemctl is-active --quiet fastpanel 2>/dev/null; then
        log_success "FastPanel service is running"
    else
        log_warning "FastPanel service may not be running"
        log_info "Starting FastPanel service..."
        systemctl start fastpanel || log_warning "Could not start FastPanel"
    fi
    
    # Check FastPanel listening port
    if netstat -tlnp | grep -q ":5501.*fastpanel"; then
        log_success "FastPanel is listening on port 5501"
        
        # Check if it's listening on all interfaces or specific IP
        if netstat -tlnp | grep ":5501" | grep -q "127.0.0.1"; then
            log_info "FastPanel listening on localhost only"
        elif netstat -tlnp | grep ":5501" | grep -q "0.0.0.0"; then
            log_info "FastPanel listening on all interfaces"
        else
            log_info "FastPanel listening configuration:"
            netstat -tlnp | grep ":5501"
        fi
    else
        log_warning "FastPanel may not be listening on port 5501"
        log_info "Current listening ports:"
        netstat -tlnp | grep fastpanel || log_info "No FastPanel processes found"
    fi
}

# Function to verify subdomain access path
verify_subdomain_access() {
    log_info "Verifying subdomain access path..."
    
    # Test local FastPanel access
    log_info "Testing local FastPanel access..."
    if curl -s --connect-timeout 5 http://127.0.0.1:5501 >/dev/null 2>&1; then
        log_success "FastPanel responds locally on port 5501"
    else
        log_warning "FastPanel may not be responding locally"
    fi
    
    # Test if FastPanel responds on backend IP (for proxy access)
    log_info "Testing FastPanel access on backend IP..."
    if curl -s --connect-timeout 5 http://${BACKEND_SERVER_IP}:5501 >/dev/null 2>&1; then
        log_success "FastPanel responds on backend IP (good for proxy)"
    else
        log_warning "FastPanel may not respond on backend IP"
    fi
    
    # Test subdomain resolution
    log_info "Testing subdomain resolution..."
    if nslookup ${FASTPANEL_SUBDOMAIN} >/dev/null 2>&1; then
        log_success "Subdomain ${FASTPANEL_SUBDOMAIN} resolves"
        log_info "Subdomain points to:"
        nslookup ${FASTPANEL_SUBDOMAIN} | grep -A1 "Name:" || true
    else
        log_warning "Subdomain ${FASTPANEL_SUBDOMAIN} may not resolve"
    fi
}

# Function to show proxy configuration requirements
show_proxy_requirements() {
    log_info "Proxy Server Configuration Requirements:"
    echo "========================================"
    
    echo ""
    log_info "🔧 Your proxy server (*************) needs this Nginx configuration:"
    echo ""
    cat << 'EOF'
# Add this to your proxy server Nginx configuration:

# FastPanel subdomain
server {
    listen 80;
    listen 443 ssl http2;
    server_name fastpanel.streamdb.online;
    
    # SSL configuration (if using HTTPS)
    # ssl_certificate /path/to/your/ssl/cert.pem;
    # ssl_certificate_key /path/to/your/ssl/key.pem;
    
    # Proxy to backend FastPanel
    location / {
        proxy_pass http://***********:5501;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # FastPanel specific headers
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Server $host;
        
        # Timeouts for FastPanel
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
EOF
    
    echo ""
    log_info "🔧 After adding this configuration to your proxy server:"
    log_info "1. Test Nginx config: nginx -t"
    log_info "2. Reload Nginx: systemctl reload nginx"
    log_info "3. Test access: https://fastpanel.streamdb.online/"
}

# Function to create verification script
create_subdomain_verification() {
    log_info "Creating subdomain verification script..."
    
    cat > /root/verify-fastpanel-subdomain.sh << 'EOF'
#!/bin/bash

# FastPanel Subdomain Access Verification Script

echo "🔍 Verifying FastPanel subdomain access..."
echo "=========================================="

BACKEND_IP="***********"
FASTPANEL_SUBDOMAIN="fastpanel.streamdb.online"

# Test local FastPanel
echo ""
echo "Testing local FastPanel access:"
if curl -s --connect-timeout 5 http://127.0.0.1:5501 >/dev/null 2>&1; then
    echo "✅ FastPanel: Responds locally"
else
    echo "❌ FastPanel: Not responding locally"
fi

# Test FastPanel on backend IP (for proxy)
echo ""
echo "Testing FastPanel on backend IP (for proxy access):"
if curl -s --connect-timeout 5 http://${BACKEND_IP}:5501 >/dev/null 2>&1; then
    echo "✅ FastPanel: Accessible on backend IP (good for proxy)"
else
    echo "❌ FastPanel: Not accessible on backend IP"
fi

# Test subdomain resolution
echo ""
echo "Testing subdomain resolution:"
if nslookup ${FASTPANEL_SUBDOMAIN} >/dev/null 2>&1; then
    echo "✅ Subdomain: Resolves correctly"
    echo "   Points to: $(nslookup ${FASTPANEL_SUBDOMAIN} | grep -A1 'Name:' | tail -1 | awk '{print $2}' || echo 'Unknown')"
else
    echo "❌ Subdomain: Does not resolve"
fi

# Test subdomain access (if accessible)
echo ""
echo "Testing subdomain access:"
if curl -s --connect-timeout 10 https://${FASTPANEL_SUBDOMAIN} >/dev/null 2>&1; then
    echo "✅ Subdomain access: Working"
elif curl -s --connect-timeout 10 http://${FASTPANEL_SUBDOMAIN} >/dev/null 2>&1; then
    echo "✅ Subdomain access: Working (HTTP)"
else
    echo "⚠️  Subdomain access: Not accessible (check proxy configuration)"
fi

# Test main website
echo ""
echo "Testing main website:"
if curl -s --connect-timeout 10 https://streamdb.online >/dev/null 2>&1; then
    echo "✅ Main website: Accessible"
else
    echo "❌ Main website: Not accessible"
fi

# Show firewall status
echo ""
echo "Current firewall rules:"
ufw status numbered | head -20

echo ""
echo "🎯 Access URLs:"
echo "   FastPanel: https://fastpanel.streamdb.online/"
echo "   Website: https://streamdb.online"
echo "   Admin Panel: https://streamdb.online/admin"

echo ""
echo "🔧 If FastPanel subdomain doesn't work:"
echo "   1. Check proxy server Nginx configuration"
echo "   2. Ensure subdomain DNS points to proxy server"
echo "   3. Verify SSL certificates (if using HTTPS)"
echo "   4. Check proxy server firewall allows port 5501"
EOF

    chmod +x /root/verify-fastpanel-subdomain.sh
    log_success "Subdomain verification script created at /root/verify-fastpanel-subdomain.sh"
}

# Main execution
main() {
    log_info "🔧 FastPanel Subdomain Access Configuration"
    log_info "==========================================="
    log_info "Configuring for: https://${FASTPANEL_SUBDOMAIN}/"
    
    # Check prerequisites
    check_root
    
    # Configure for subdomain access
    configure_subdomain_firewall
    check_fastpanel_config
    verify_subdomain_access
    create_subdomain_verification
    show_proxy_requirements
    
    echo ""
    log_success "🎉 FastPanel subdomain configuration completed!"
    log_info ""
    log_info "🔍 Next steps:"
    log_info "1. Configure proxy server Nginx (see requirements above)"
    log_info "2. Test FastPanel: https://fastpanel.streamdb.online/"
    log_info "3. Test website: https://streamdb.online"
    log_info "4. Run verification: ./verify-fastpanel-subdomain.sh"
    log_info ""
    log_success "✅ Your reverse proxy architecture is maintained:"
    log_info "   Browser → Cloudflare → ************* → ***********:5501"
    log_info ""
    log_warning "⚠️  Security Benefits:"
    log_info "   - FastPanel not directly accessible from internet"
    log_info "   - All traffic flows through secure proxy chain"
    log_info "   - Backend IP remains hidden"
    log_info "   - Maximum security with full functionality"
}

# Run main function
main "$@"
