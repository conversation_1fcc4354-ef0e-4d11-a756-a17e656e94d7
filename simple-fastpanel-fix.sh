#!/bin/bash

# Simple FastPanel Fix - No overthinking

echo "=== FastPanel Simple Fix ==="

# 1. Check what's actually running
echo "Current FastPanel processes:"
ps aux | grep fastpanel | grep -v grep

echo ""
echo "What's listening on port 5501:"
netstat -tlnp | grep 5501

echo ""
echo "Checking systemd services:"
systemctl status fastpanel 2>/dev/null || echo "No fastpanel service"

echo ""
echo "Starting FastPanel the simple way:"

# 2. Try the most basic approach - restart the service
if systemctl restart fastpanel 2>/dev/null; then
    echo "✅ FastPanel service restarted"
    sleep 3
    
    # Check if it's working
    if netstat -tlnp | grep -q 5501; then
        echo "✅ FastPanel is now listening on port 5501"
        netstat -tlnp | grep 5501
        echo ""
        echo "Testing access:"
        curl -I http://127.0.0.1:5501 2>/dev/null | head -1 || echo "❌ Not responding"
    else
        echo "❌ FastPanel not listening on port 5501"
    fi
else
    echo "❌ Could not restart fastpanel service"
    
    # Try manual start
    echo "Trying manual start..."
    /usr/local/fastpanel2/fastpanel start 2>&1 || echo "Manual start failed"
    
    sleep 3
    netstat -tlnp | grep 5501 || echo "Still not listening"
fi

echo ""
echo "=== Current Status ==="
ps aux | grep fastpanel | grep -v grep
netstat -tlnp | grep 5501
