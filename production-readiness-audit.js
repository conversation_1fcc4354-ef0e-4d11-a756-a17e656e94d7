#!/usr/bin/env node

/**
 * StreamDB Production Readiness Audit & Fix Script
 * Comprehensive audit and fix for production deployment issues
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logHeader(message) {
  log(`\n${'='.repeat(60)}`, 'cyan');
  log(`🔍 ${message}`, 'cyan');
  log(`${'='.repeat(60)}`, 'cyan');
}

class ProductionAudit {
  constructor() {
    this.issues = [];
    this.fixes = [];
    this.warnings = [];
  }

  // Phase 1: Immediate Error Resolution
  async phase1_immediateErrors() {
    logHeader('Phase 1: Immediate Error Resolution');
    
    await this.checkBuildStatus();
    await this.checkServerConfiguration();
    await this.fixLocalStorageIssues();
    await this.verifyStaticAssets();
  }

  async checkBuildStatus() {
    logInfo('Checking build status...');
    
    // Check if dist directory exists
    if (!fs.existsSync('dist')) {
      logError('Dist directory not found');
      this.issues.push('Missing dist directory');
      
      logInfo('Building application...');
      try {
        execSync('npm run build', { stdio: 'inherit' });
        logSuccess('Application built successfully');
        this.fixes.push('Built application');
      } catch (error) {
        logError('Build failed');
        this.issues.push('Build failure');
        return false;
      }
    } else {
      logSuccess('Dist directory exists');
    }

    // Check if index.html references built assets
    const indexPath = path.join('dist', 'index.html');
    if (fs.existsSync(indexPath)) {
      const indexContent = fs.readFileSync(indexPath, 'utf8');
      if (indexContent.includes('/assets/')) {
        logSuccess('index.html correctly references built assets');
      } else {
        logError('index.html does not reference built assets');
        this.issues.push('index.html references development files');
      }
    }

    return true;
  }

  async checkServerConfiguration() {
    logInfo('Checking server configuration...');
    
    const serverPath = path.join('server', 'index.js');
    if (!fs.existsSync(serverPath)) {
      logError('Server file not found');
      this.issues.push('Missing server/index.js');
      return false;
    }

    const serverContent = fs.readFileSync(serverPath, 'utf8');
    
    // Check for static file serving
    if (serverContent.includes('express.static') && serverContent.includes('dist')) {
      logSuccess('Server configured for static file serving');
    } else {
      logWarning('Server may not be configured for static file serving');
      this.warnings.push('Static file serving configuration unclear');
    }

    // Check for MIME type configuration
    if (serverContent.includes('Content-Type') && serverContent.includes('application/javascript')) {
      logSuccess('Server has MIME type configuration');
    } else {
      logWarning('Server may be missing MIME type configuration');
      this.warnings.push('MIME type configuration unclear');
    }

    return true;
  }

  async fixLocalStorageIssues() {
    logInfo('Checking localStorage usage...');
    
    // Check main.tsx for localStorage issues
    const mainPath = path.join('src', 'main.tsx');
    if (fs.existsSync(mainPath)) {
      const mainContent = fs.readFileSync(mainPath, 'utf8');
      if (mainContent.includes('safeLocalStorage')) {
        logSuccess('Safe localStorage wrapper implemented');
      } else {
        logWarning('localStorage safety wrapper may be missing');
        this.warnings.push('localStorage safety not confirmed');
      }
    }
  }

  async verifyStaticAssets() {
    logInfo('Verifying static assets...');
    
    const assetsPath = path.join('dist', 'assets');
    if (fs.existsSync(assetsPath)) {
      const files = fs.readdirSync(assetsPath);
      const jsFiles = files.filter(f => f.endsWith('.js'));
      const cssFiles = files.filter(f => f.endsWith('.css'));
      
      logSuccess(`Found ${jsFiles.length} JavaScript files and ${cssFiles.length} CSS files`);
      
      if (jsFiles.length === 0) {
        logError('No JavaScript files found in assets');
        this.issues.push('Missing JavaScript assets');
      }
      
      if (cssFiles.length === 0) {
        logError('No CSS files found in assets');
        this.issues.push('Missing CSS assets');
      }
    } else {
      logError('Assets directory not found');
      this.issues.push('Missing assets directory');
    }
  }

  // Phase 2: Codebase Quality Audit
  async phase2_codebaseAudit() {
    logHeader('Phase 2: Codebase Quality Audit');
    
    await this.scanForDuplicates();
    await this.checkDevelopmentReferences();
    await this.verifyDatabaseMigration();
  }

  async scanForDuplicates() {
    logInfo('Scanning for duplicate files...');
    
    // Check for common duplicate patterns
    const duplicatePatterns = [
      'index.js',
      'main.tsx',
      'App.tsx',
      'package.json'
    ];
    
    for (const pattern of duplicatePatterns) {
      const matches = this.findFiles('.', pattern);
      if (matches.length > 1) {
        logWarning(`Multiple ${pattern} files found: ${matches.join(', ')}`);
        this.warnings.push(`Duplicate ${pattern} files`);
      }
    }
  }

  findFiles(dir, filename) {
    const results = [];
    const files = fs.readdirSync(dir, { withFileTypes: true });
    
    for (const file of files) {
      if (file.isDirectory() && !file.name.startsWith('.') && file.name !== 'node_modules') {
        results.push(...this.findFiles(path.join(dir, file.name), filename));
      } else if (file.name === filename) {
        results.push(path.join(dir, file.name));
      }
    }
    
    return results;
  }

  async checkDevelopmentReferences() {
    logInfo('Checking for development references...');
    
    const devPatterns = [
      'localhost:5173',
      'localhost:8080',
      'http://localhost',
      'vite dev',
      'development'
    ];
    
    // Check key files for development references
    const filesToCheck = [
      'vite.config.ts',
      'server/index.js',
      'src/config/auth.ts'
    ];
    
    for (const file of filesToCheck) {
      if (fs.existsSync(file)) {
        const content = fs.readFileSync(file, 'utf8');
        for (const pattern of devPatterns) {
          if (content.includes(pattern) && !content.includes('NODE_ENV')) {
            logWarning(`Development reference found in ${file}: ${pattern}`);
            this.warnings.push(`Development reference in ${file}`);
          }
        }
      }
    }
  }

  async verifyDatabaseMigration() {
    logInfo('Verifying localStorage to database migration...');
    
    // Check if migration files exist
    const migrationFiles = [
      'database/localStorage_migration_schema.sql',
      'server/services/storageService.js',
      'LOCALSTORAGE_TO_DATABASE_MIGRATION_GUIDE.md'
    ];
    
    let migrationComplete = true;
    for (const file of migrationFiles) {
      if (fs.existsSync(file)) {
        logSuccess(`Migration file exists: ${file}`);
      } else {
        logWarning(`Migration file missing: ${file}`);
        migrationComplete = false;
      }
    }
    
    if (migrationComplete) {
      logSuccess('Database migration files are present');
    } else {
      this.warnings.push('Database migration may be incomplete');
    }
  }

  // Generate summary report
  generateReport() {
    logHeader('Production Readiness Audit Summary');
    
    log(`\n📊 AUDIT RESULTS:`, 'cyan');
    log(`   Issues Found: ${this.issues.length}`, this.issues.length > 0 ? 'red' : 'green');
    log(`   Warnings: ${this.warnings.length}`, this.warnings.length > 0 ? 'yellow' : 'green');
    log(`   Fixes Applied: ${this.fixes.length}`, 'green');
    
    if (this.issues.length > 0) {
      log(`\n❌ CRITICAL ISSUES:`, 'red');
      this.issues.forEach((issue, i) => log(`   ${i + 1}. ${issue}`, 'red'));
    }
    
    if (this.warnings.length > 0) {
      log(`\n⚠️  WARNINGS:`, 'yellow');
      this.warnings.forEach((warning, i) => log(`   ${i + 1}. ${warning}`, 'yellow'));
    }
    
    if (this.fixes.length > 0) {
      log(`\n✅ FIXES APPLIED:`, 'green');
      this.fixes.forEach((fix, i) => log(`   ${i + 1}. ${fix}`, 'green'));
    }
    
    log(`\n🚀 NEXT STEPS:`, 'cyan');
    log(`   1. Deploy to production server`, 'blue');
    log(`   2. Test on https://streamdb.online`, 'blue');
    log(`   3. Verify mobile responsiveness`, 'blue');
    log(`   4. Check browser console for errors`, 'blue');
  }

  async run() {
    log('🔍 Starting StreamDB Production Readiness Audit...', 'cyan');
    
    await this.phase1_immediateErrors();
    await this.phase2_codebaseAudit();
    
    this.generateReport();
  }
}

// Run the audit
const audit = new ProductionAudit();
audit.run().catch(console.error);

export default ProductionAudit;
