/**
 * Ad Blocker Awareness Popup Tracking System
 *
 * This module handles session tracking for the ad blocker awareness popup,
 * using database storage for persistence and cross-device synchronization.
 */

// Constants
const DISPLAY_INTERVAL_HOURS = 24;
const DISPLAY_INTERVAL_MS = DISPLAY_INTERVAL_HOURS * 60 * 60 * 1000; // 24 hours in milliseconds

// API Configuration
const API_BASE_URL = process.env.NODE_ENV === 'production'
  ? '/api'  // Production: same domain
  : 'http://localhost:3001/api';  // Development: local server

// Check if we're in a preview/build environment without backend
const isPreviewMode = typeof window !== 'undefined' &&
  (window.location.port === '4173' || window.location.hostname === 'localhost');

// Safe database API wrapper to handle connection failures
const safeDatabaseAPI = {
  async get(endpoint: string): Promise<any> {
    // In preview mode, return mock data
    if (isPreviewMode && !window.location.search.includes('use-api=true')) {
      return { success: true, data: null };
    }

    try {
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        method: 'GET',
        credentials: 'include', // Include cookies for session management
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.warn('Database API GET failed:', error);
      return null;
    }
  },

  async post(endpoint: string, data: any): Promise<boolean> {
    // In preview mode, return success without making API call
    if (isPreviewMode && !window.location.search.includes('use-api=true')) {
      console.log('Mock API POST:', endpoint, data);
      return true;
    }

    try {
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        method: 'POST',
        credentials: 'include', // Include cookies for session management
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`);
      }

      return true;
    } catch (error) {
      console.warn('Database API POST failed:', error);
      return false;
    }
  },

  async delete(endpoint: string): Promise<boolean> {
    try {
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        method: 'DELETE',
        credentials: 'include', // Include cookies for session management
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`);
      }

      return true;
    } catch (error) {
      console.warn('Database API DELETE failed:', error);
      return false;
    }
  }
};

// Types for future database integration
export interface AwarenessTrackingRecord {
  userId?: string; // For future database integration
  sessionId: string;
  lastShownTimestamp: number;
  dismissCount: number;
  userAgent: string;
  createdAt: number;
  updatedAt: number;
}

export interface AwarenessTrackingOptions {
  forceShow?: boolean; // For testing purposes
  customInterval?: number; // Custom interval in hours
  useDatabase?: boolean; // Future flag for database integration
}

/**
 * Generate a unique session ID for tracking
 */
function generateSessionId(): string {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Get current tracking record from database
 */
async function getDatabaseRecord(): Promise<AwarenessTrackingRecord | null> {
  try {
    const response = await safeDatabaseAPI.get('/tracking/ad-blocker');
    if (!response || !response.success) {
      return null;
    }

    const record = response.data;

    // Validate record structure
    if (typeof record.last_shown_timestamp !== 'number' ||
        typeof record.dismiss_count !== 'number') {
      console.warn('Invalid awareness tracking record from database');
      return null;
    }

    // Convert database format to interface format
    return {
      sessionId: record.session_id,
      lastShownTimestamp: record.last_shown_timestamp,
      dismissCount: record.dismiss_count,
      userAgent: record.user_agent || navigator.userAgent,
      createdAt: new Date(record.created_at).getTime(),
      updatedAt: new Date(record.updated_at).getTime()
    };
  } catch (error) {
    console.error('Error reading awareness tracking record from database:', error);
    return null;
  }
}

/**
 * Save tracking record to database
 */
async function saveDatabaseRecord(record: AwarenessTrackingRecord): Promise<boolean> {
  try {
    const success = await safeDatabaseAPI.post('/tracking/ad-blocker', {
      sessionId: record.sessionId,
      lastShownTimestamp: record.lastShownTimestamp,
      dismissCount: record.dismissCount,
      userAgent: record.userAgent
    });

    if (!success) {
      console.warn('Failed to save awareness tracking record to database');
    }

    return success;
  } catch (error) {
    console.error('Error saving awareness tracking record to database:', error);
    return false;
  }
}

/**
 * Create a new tracking record
 */
function createNewRecord(): AwarenessTrackingRecord {
  const now = Date.now();
  return {
    sessionId: generateSessionId(),
    lastShownTimestamp: 0, // Will be set when popup is shown
    dismissCount: 0,
    userAgent: navigator.userAgent,
    createdAt: now,
    updatedAt: now
  };
}

/**
 * Check if popup should be shown based on timing rules
 */
export async function shouldShowAwarenessPopup(options: AwarenessTrackingOptions = {}): Promise<boolean> {
  // Force show for testing
  if (options.forceShow) {
    return true;
  }

  const record = await getDatabaseRecord();

  // If no record exists, show popup (first visit)
  if (!record) {
    return true;
  }

  // Check if enough time has passed since last shown
  const now = Date.now();
  const intervalMs = options.customInterval
    ? options.customInterval * 60 * 60 * 1000
    : DISPLAY_INTERVAL_MS;

  const timeSinceLastShown = now - record.lastShownTimestamp;

  return timeSinceLastShown >= intervalMs;
}

/**
 * Record that the popup was shown
 */
export async function recordPopupShown(): Promise<boolean> {
  try {
    const success = await safeDatabaseAPI.post('/tracking/ad-blocker/shown', {
      timestamp: Date.now()
    });

    if (success) {
      console.log('Ad blocker awareness popup shown and recorded');
    } else {
      console.warn('Failed to record popup shown');
    }

    return success;
  } catch (error) {
    console.error('Error recording popup shown:', error);
    return false;
  }
}

/**
 * Record that the popup was dismissed by user
 */
export async function recordPopupDismissed(): Promise<boolean> {
  try {
    const success = await safeDatabaseAPI.post('/tracking/ad-blocker/dismissed', {
      timestamp: Date.now()
    });

    if (success) {
      console.log('Ad blocker awareness popup dismissed');
    } else {
      console.warn('Failed to record popup dismissed');
    }

    return success;
  } catch (error) {
    console.error('Error recording popup dismissed:', error);
    return false;
  }
}

/**
 * Get tracking statistics for analytics
 */
export async function getTrackingStats(): Promise<{
  hasRecord: boolean;
  lastShown: Date | null;
  dismissCount: number;
  daysSinceLastShown: number;
  sessionId: string | null;
}> {
  const record = await getDatabaseRecord();

  if (!record) {
    return {
      hasRecord: false,
      lastShown: null,
      dismissCount: 0,
      daysSinceLastShown: -1,
      sessionId: null
    };
  }

  const daysSinceLastShown = record.lastShownTimestamp > 0
    ? Math.floor((Date.now() - record.lastShownTimestamp) / (1000 * 60 * 60 * 24))
    : -1;

  return {
    hasRecord: true,
    lastShown: record.lastShownTimestamp > 0 ? new Date(record.lastShownTimestamp) : null,
    dismissCount: record.dismissCount,
    daysSinceLastShown,
    sessionId: record.sessionId
  };
}

/**
 * Reset tracking data (for testing or user request)
 */
export async function resetTrackingData(): Promise<boolean> {
  try {
    const success = await safeDatabaseAPI.delete('/tracking/ad-blocker');
    if (success) {
      console.log('Ad blocker awareness tracking data reset');
    } else {
      console.warn('Failed to reset ad blocker awareness tracking data');
    }
    return success;
  } catch (error) {
    console.error('Error resetting tracking data:', error);
    return false;
  }
}

/**
 * Future database integration functions
 * These will be implemented when database connectivity is added
 */

/**
 * Save tracking record to database
 */
export async function saveToDatabase(record: AwarenessTrackingRecord): Promise<boolean> {
  return await saveDatabaseRecord(record);
}

/**
 * Load tracking record from database
 */
export async function loadFromDatabase(userId?: string): Promise<AwarenessTrackingRecord | null> {
  return await getDatabaseRecord();
}

/**
 * Sync with database (database is now the primary storage)
 */
export async function syncWithDatabase(userId?: string): Promise<boolean> {
  try {
    // Database is now the primary storage, so sync is automatic
    const record = await getDatabaseRecord();
    return record !== null;
  } catch (error) {
    console.error('Error syncing with database:', error);
    return false;
  }
}

/**
 * Migration is complete - database is now the primary storage
 */
export async function migrateToDatabase(userId?: string): Promise<boolean> {
  // Migration is complete - all operations now use database
  console.log('Migration complete - using database storage');
  return true;
}

/**
 * Get next show time for user information
 */
export async function getNextShowTime(): Promise<Date | null> {
  const record = await getDatabaseRecord();
  if (!record || record.lastShownTimestamp === 0) {
    return null; // Will show immediately
  }

  return new Date(record.lastShownTimestamp + DISPLAY_INTERVAL_MS);
}

/**
 * Check if user has dismissed popup multiple times (for analytics)
 */
export async function isFrequentDismisser(): Promise<boolean> {
  const record = await getDatabaseRecord();
  return record ? record.dismissCount >= 3 : false;
}

/**
 * Development/testing utilities
 */
export const devUtils = {
  /**
   * Force show popup (for testing)
   */
  forceShow: () => shouldShowAwarenessPopup({ forceShow: true }),

  /**
   * Set custom interval (for testing)
   */
  setTestInterval: (hours: number) => shouldShowAwarenessPopup({ customInterval: hours }),

  /**
   * Get raw record data
   */
  getRawRecord: () => getDatabaseRecord(),

  /**
   * Manually set last shown time
   */
  setLastShown: async (timestamp: number) => {
    const record = await getDatabaseRecord() || createNewRecord();
    record.lastShownTimestamp = timestamp;
    record.updatedAt = Date.now();
    await saveDatabaseRecord(record);
  }
};
