-- ============================================================================
-- StreamDB Online - Complete Database Schema
-- Consolidated schema aligned with Admin Panel requirements
-- NO CLEANUP of Admin Panel managed content
-- ============================================================================

-- Create database (run this first in phpMyAdmin)
-- CREATE DATABASE IF NOT EXISTS streamdb_database CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- USE streamdb_database;

-- Categories table for content classification
CREATE TABLE categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,
    type ENUM('movie', 'series', 'both') DEFAULT 'both',
    slug VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_type (type),
    INDEX idx_active (is_active),
    INDEX idx_slug (slug)
);

-- Main content table - aligned with Admin Panel form fields
CREATE TABLE content (
    id VARCHAR(50) PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    year INT NOT NULL,
    type ENUM('movie', 'series', 'requested') NOT NULL,
    category VARCHAR(100), -- Maps to Admin Panel category field
    image VARCHAR(500),
    cover_image VARCHAR(500),
    
    -- Enhanced metadata fields from Admin Panel
    tmdb_id VARCHAR(20),
    poster_url VARCHAR(500),
    thumbnail_url VARCHAR(500),
    secure_video_links TEXT, -- Encrypted/encoded video links for security
    imdb_rating DECIMAL(3,1),
    runtime VARCHAR(20),
    studio VARCHAR(255),
    tags TEXT,
    trailer VARCHAR(500),
    subtitle_url VARCHAR(500),
    
    -- Publishing and feature flags from Admin Panel
    is_published BOOLEAN DEFAULT FALSE,
    is_featured BOOLEAN DEFAULT FALSE,
    add_to_carousel BOOLEAN DEFAULT FALSE,
    
    -- Web series specific fields
    total_seasons INT DEFAULT 0,
    total_episodes INT DEFAULT 0,
    
    -- JSON fields for flexible array storage (Admin Panel arrays)
    languages JSON, -- ["Hindi", "English", "Tamil"]
    genres JSON,    -- ["Action", "Adventure", "Comedy"]
    quality JSON,   -- ["HD", "WEB", "BluRay"]
    audio_tracks JSON, -- ["Hindi", "English", "Tamil"]
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes for performance
    INDEX idx_type (type),
    INDEX idx_year (year),
    INDEX idx_published (is_published),
    INDEX idx_featured (is_featured),
    INDEX idx_carousel (add_to_carousel),
    INDEX idx_created_at (created_at),
    INDEX idx_tmdb_id (tmdb_id),
    INDEX idx_category (category),
    
    -- Full-text search index
    FULLTEXT KEY ft_search (title, description, tags)
);

-- Seasons table for web series
CREATE TABLE seasons (
    id VARCHAR(50) PRIMARY KEY,
    content_id VARCHAR(50) NOT NULL,
    season_number INT NOT NULL,
    title VARCHAR(255),
    description TEXT,
    poster_url VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (content_id) REFERENCES content(id) ON DELETE CASCADE,
    UNIQUE KEY unique_season (content_id, season_number),
    INDEX idx_content_season (content_id, season_number)
);

-- Episodes table for web series
CREATE TABLE episodes (
    id VARCHAR(50) PRIMARY KEY,
    season_id VARCHAR(50) NOT NULL,
    content_id VARCHAR(50) NOT NULL,
    episode_number INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    secure_video_links TEXT, -- Encrypted/encoded video links for security
    runtime VARCHAR(20),
    air_date DATE,
    thumbnail_url VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (season_id) REFERENCES seasons(id) ON DELETE CASCADE,
    FOREIGN KEY (content_id) REFERENCES content(id) ON DELETE CASCADE,
    UNIQUE KEY unique_episode (season_id, episode_number),
    INDEX idx_season_episode (season_id, episode_number),
    INDEX idx_content_episode (content_id, episode_number)
);

-- Admin users table for authentication
CREATE TABLE admin_users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(100) UNIQUE,
    role ENUM('admin', 'moderator') DEFAULT 'admin',
    permissions JSON,
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    login_attempts INT DEFAULT 0,
    locked_until TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_active (is_active)
);

-- ============================================================================
-- SESSION MANAGEMENT TABLES (replaces localStorage)
-- ============================================================================

-- User Sessions Table (replaces localStorage session management)
CREATE TABLE user_sessions (
    id VARCHAR(255) PRIMARY KEY,
    user_id VARCHAR(255) NULL, -- NULL for anonymous sessions
    session_data JSON NOT NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_expires_at (expires_at),
    INDEX idx_created_at (created_at)
);

-- Ad Blocker Awareness Tracking Table
CREATE TABLE ad_blocker_tracking (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    user_id VARCHAR(255) NULL, -- For future user association
    last_shown_timestamp BIGINT NOT NULL DEFAULT 0,
    dismiss_count INT NOT NULL DEFAULT 0,
    user_agent TEXT NULL,
    ip_address VARCHAR(45) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_session (session_id),
    INDEX idx_user_id (user_id),
    INDEX idx_last_shown (last_shown_timestamp),
    INDEX idx_created_at (created_at)
);

-- Login Attempts Table (replaces localStorage login tracking)
CREATE TABLE login_attempts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    username VARCHAR(255) NULL, -- Attempted username
    success BOOLEAN NOT NULL DEFAULT FALSE,
    failure_reason VARCHAR(100) NULL,
    timestamp BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_session_id (session_id),
    INDEX idx_ip_address (ip_address),
    INDEX idx_timestamp (timestamp),
    INDEX idx_success (success),
    INDEX idx_created_at (created_at)
);

-- Security Event Logs Table (replaces localStorage security logs)
CREATE TABLE security_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    user_id VARCHAR(255) NULL,
    event_type ENUM(
        'LOGIN_SUCCESS', 'LOGIN_FAILED', 'LOGOUT', 'SESSION_EXPIRED',
        'SESSION_REFRESHED', 'ACCOUNT_LOCKED', 'SECURITY_VIOLATION'
    ) NOT NULL,
    severity ENUM('low', 'medium', 'high', 'critical') NOT NULL DEFAULT 'medium',
    details JSON NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    timestamp BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_session_id (session_id),
    INDEX idx_user_id (user_id),
    INDEX idx_event_type (event_type),
    INDEX idx_severity (severity),
    INDEX idx_timestamp (timestamp),
    INDEX idx_created_at (created_at)
);

-- Authentication Tokens Table (replaces localStorage token storage)
CREATE TABLE auth_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    token_hash VARCHAR(255) NOT NULL, -- Hashed token for security
    session_id VARCHAR(255) NOT NULL,
    user_id VARCHAR(255) NOT NULL,
    token_type ENUM('access', 'refresh') NOT NULL DEFAULT 'access',
    expires_at TIMESTAMP NOT NULL,
    is_revoked BOOLEAN NOT NULL DEFAULT FALSE,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_token_hash (token_hash),
    INDEX idx_session_id (session_id),
    INDEX idx_user_id (user_id),
    INDEX idx_token_type (token_type),
    INDEX idx_expires_at (expires_at),
    INDEX idx_is_revoked (is_revoked)
);



-- ============================================================================
-- SAFE CLEANUP PROCEDURES (ONLY FOR SESSIONS AND TOKENS)
-- ============================================================================

-- Session Cleanup Procedure (ONLY removes expired sessions and tokens)
DELIMITER //
CREATE PROCEDURE CleanupExpiredSessions()
BEGIN
    -- Remove expired user sessions
    DELETE FROM user_sessions WHERE expires_at < NOW();
    
    -- Remove expired auth tokens
    DELETE FROM auth_tokens WHERE expires_at < NOW();
    
    -- Remove old login attempts (older than 7 days)
    DELETE FROM login_attempts WHERE created_at < DATE_SUB(NOW(), INTERVAL 7 DAY);
    
    -- Remove old security logs (older than 30 days)
    DELETE FROM security_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    -- Remove old ad blocker tracking (older than 90 days)
    DELETE FROM ad_blocker_tracking WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY);
END //
DELIMITER ;

-- ============================================================================
-- INITIAL DATA AND CONFIGURATION
-- ============================================================================



-- Insert default categories matching Admin Panel categories
INSERT IGNORE INTO categories (name, type, slug, description) VALUES
('Hindi Movies', 'movie', 'hindi-movies', 'Hindi language movies'),
('Hindi Web Series', 'series', 'hindi-web-series', 'Hindi language web series'),
('English Movies', 'movie', 'english-movies', 'English language movies'),
('English Web Series', 'series', 'english-web-series', 'English language web series'),
('Telugu Movies', 'movie', 'telugu-movies', 'Telugu language movies'),
('Telugu Web Series', 'series', 'telugu-web-series', 'Telugu language web series'),
('Tamil Movies', 'movie', 'tamil-movies', 'Tamil language movies'),
('Tamil Web Series', 'series', 'tamil-web-series', 'Tamil language web series'),
('Malayalam Movies', 'movie', 'malayalam-movies', 'Malayalam language movies'),
('Malayalam Web Series', 'series', 'malayalam-web-series', 'Malayalam language web series'),
('Korean Movies', 'movie', 'korean-movies', 'Korean language movies'),
('Korean Web Series', 'series', 'korean-web-series', 'Korean language web series'),
('Japanese Movies', 'movie', 'japanese-movies', 'Japanese language movies'),
('Japanese Web Series', 'series', 'japanese-web-series', 'Japanese language web series'),
('Anime', 'both', 'anime', 'Anime movies and series'),
('Hindi Dubbed', 'both', 'hindi-dubbed', 'Hindi dubbed content'),
('English Dubbed', 'both', 'english-dubbed', 'English dubbed content'),
('Animation', 'both', 'animation', 'Animated movies and series');

-- Create events for automatic cleanup (ONLY sessions and tokens)
CREATE EVENT IF NOT EXISTS daily_session_cleanup
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_TIMESTAMP
DO CALL CleanupExpiredSessions();

-- Enable event scheduler if not already enabled
SET GLOBAL event_scheduler = ON;

-- ============================================================================
-- VIEWS FOR ANALYTICS AND MONITORING
-- ============================================================================

-- View for recent deployment activity (last 30 days)
CREATE OR REPLACE VIEW recent_deployments AS
SELECT 
    d.id,
    d.status,
    d.branch,
    d.commits,
    d.pusher,
    d.repository,
    d.created_at,
    d.completed_at,
    TIMESTAMPDIFF(SECOND, d.created_at, COALESCE(d.completed_at, NOW())) as duration_seconds,
    d.error_message
FROM deployments d
WHERE d.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
ORDER BY d.created_at DESC;

-- View for deployment statistics
CREATE OR REPLACE VIEW deployment_stats AS
SELECT 
    COUNT(*) as total_deployments,
    SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) as successful_deployments,
    SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) as failed_deployments,
    SUM(CASE WHEN status = 'STARTED' THEN 1 ELSE 0 END) as running_deployments,
    ROUND(AVG(TIMESTAMPDIFF(SECOND, created_at, completed_at)), 2) as avg_duration_seconds,
    MAX(created_at) as last_deployment,
    COUNT(DISTINCT DATE(created_at)) as deployment_days
FROM deployments
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY);

-- View for content statistics
CREATE OR REPLACE VIEW content_stats AS
SELECT 
    COUNT(*) as total_content,
    SUM(CASE WHEN type = 'movie' THEN 1 ELSE 0 END) as total_movies,
    SUM(CASE WHEN type = 'series' THEN 1 ELSE 0 END) as total_series,
    SUM(CASE WHEN type = 'requested' THEN 1 ELSE 0 END) as total_requested,
    SUM(CASE WHEN is_published = TRUE THEN 1 ELSE 0 END) as published_content,
    SUM(CASE WHEN is_featured = TRUE THEN 1 ELSE 0 END) as featured_content,
    SUM(CASE WHEN add_to_carousel = TRUE THEN 1 ELSE 0 END) as carousel_content,
    MAX(created_at) as last_added
FROM content;

-- ============================================================================
-- SECURITY AND PERMISSIONS
-- ============================================================================

-- Note: Uncomment and adjust these grants for your specific database user
-- GRANT SELECT, INSERT, UPDATE, DELETE ON streamdb_database.* TO 'dbadmin_streamdb'@'localhost';
-- FLUSH PRIVILEGES;
