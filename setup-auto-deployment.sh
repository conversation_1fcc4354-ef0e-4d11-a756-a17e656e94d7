#!/bin/bash

# StreamDB Online - Auto-Deployment Setup Script
# This script sets up the complete GitHub auto-deployment system

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_DIR="/var/www/streamdb_onl_usr/data/www/streamdb.online"
WEBHOOK_URL="https://streamdb.online/api/webhook/github"
WEBHOOK_SECRET="249fd45813bc97e48c46d634418b77a3c3388d219b9ffcfa9e7edaaa7c28a0ef"

# Function to log messages
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check required commands
    local required_commands=("git" "node" "npm" "pm2" "apache2ctl")
    for cmd in "${required_commands[@]}"; do
        if command_exists "$cmd"; then
            log_success "$cmd is installed"
        else
            log_error "$cmd is not installed"
            exit 1
        fi
    done
    
    # Check if we're in the right directory
    if [ ! -f "package.json" ]; then
        log_error "Not in project directory. Please run from: $PROJECT_DIR"
        exit 1
    fi
    
    log_success "Prerequisites check completed"
}

# Function to install dependencies
install_dependencies() {
    log_info "Installing dependencies..."
    
    # Install frontend dependencies
    if [ -f "package.json" ]; then
        log_info "Installing frontend dependencies..."
        npm install
        log_success "Frontend dependencies installed"
    fi
    
    # Install server dependencies
    if [ -f "server/package.json" ]; then
        log_info "Installing server dependencies..."
        cd server
        npm install
        cd ..
        log_success "Server dependencies installed"
    fi
}

# Function to build frontend
build_frontend() {
    log_info "Building frontend..."
    
    if [ -f "package.json" ] && grep -q '"build"' package.json; then
        npm run build
        log_success "Frontend built successfully"
    else
        log_warning "No build script found in package.json"
    fi
}

# Function to setup PM2 processes
setup_pm2() {
    log_info "Setting up PM2 processes..."
    
    # Stop existing processes if they exist
    pm2 stop streamdb-online 2>/dev/null || true
    pm2 stop webhook-handler 2>/dev/null || true
    pm2 delete streamdb-online 2>/dev/null || true
    pm2 delete webhook-handler 2>/dev/null || true
    
    # Start main application
    log_info "Starting main application..."
    pm2 start server/index.js --name "streamdb-online" --env production
    log_success "Main application started"
    
    # Start webhook handler
    log_info "Starting webhook handler..."
    pm2 start server/services/webhook-server.js --name "webhook-handler" --env production
    log_success "Webhook handler started"
    
    # Save PM2 configuration
    pm2 save
    log_success "PM2 configuration saved"
    
    # Show PM2 status
    pm2 list
}

# Function to configure Apache
configure_apache() {
    log_info "Configuring Apache for webhook proxy..."
    
    # Check if Apache modules are enabled
    if ! apache2ctl -M | grep -q "proxy_module"; then
        log_info "Enabling Apache proxy modules..."
        sudo a2enmod proxy
        sudo a2enmod proxy_http
        log_success "Apache proxy modules enabled"
    fi
    
    # Create Apache configuration snippet
    cat > /tmp/webhook-proxy.conf << 'EOF'
# Webhook proxy configuration for StreamDB Online
# Add this to your Apache virtual host configuration

# Proxy webhook requests to webhook server
ProxyPass /api/webhook/ http://localhost:9000/api/webhook/
ProxyPassReverse /api/webhook/ http://localhost:9000/api/webhook/

# Proxy other API requests to main server
ProxyPass /api/ http://localhost:3001/api/
ProxyPassReverse /api/ http://localhost:3001/api/

# Preserve host headers
ProxyPreserveHost On
EOF
    
    log_success "Apache configuration created at /tmp/webhook-proxy.conf"
    log_warning "Please add the contents of /tmp/webhook-proxy.conf to your Apache virtual host"
    log_warning "Then run: sudo systemctl reload apache2"
}

# Function to test the setup
test_setup() {
    log_info "Testing the setup..."
    
    # Test if processes are running
    if pm2 list | grep -q "streamdb-online.*online"; then
        log_success "Main application is running"
    else
        log_error "Main application is not running"
    fi
    
    if pm2 list | grep -q "webhook-handler.*online"; then
        log_success "Webhook handler is running"
    else
        log_error "Webhook handler is not running"
    fi
    
    # Test webhook endpoint (local)
    log_info "Testing webhook endpoint..."
    if curl -s -f http://localhost:9000/health > /dev/null; then
        log_success "Webhook server is responding"
    else
        log_warning "Webhook server may not be responding on localhost:9000"
    fi
    
    # Test main application endpoint (local)
    log_info "Testing main application endpoint..."
    if curl -s -f http://localhost:3001/api/health > /dev/null; then
        log_success "Main application is responding"
    else
        log_warning "Main application may not be responding on localhost:3001"
    fi
}

# Function to display GitHub webhook configuration
show_github_config() {
    log_info "GitHub Webhook Configuration:"
    echo "================================"
    echo "Payload URL: $WEBHOOK_URL"
    echo "Content type: application/json"
    echo "Secret: $WEBHOOK_SECRET"
    echo "Events: Just the push event"
    echo "Active: Yes"
    echo ""
    log_info "Add this webhook to your GitHub repository:"
    log_info "https://github.com/aakash171088/Streaming_DB/settings/hooks"
}

# Function to show next steps
show_next_steps() {
    log_success "Auto-deployment setup completed!"
    echo ""
    log_info "Next steps:"
    echo "1. Add the Apache configuration to your virtual host"
    echo "2. Reload Apache: sudo systemctl reload apache2"
    echo "3. Configure the GitHub webhook with the provided settings"
    echo "4. Test with a small commit to the New-Main-1 branch"
    echo "5. Monitor deployment logs: tail -f /var/log/streamdb-deploy.log"
    echo ""
    log_info "Admin panel: https://streamdb.online/admin"
    log_info "Webhook test: https://streamdb.online/api/webhook/test"
}

# Main execution
main() {
    echo "🚀 StreamDB Online - Auto-Deployment Setup"
    echo "=========================================="
    echo ""
    
    check_prerequisites
    install_dependencies
    build_frontend
    setup_pm2
    configure_apache
    test_setup
    
    echo ""
    show_github_config
    echo ""
    show_next_steps
}

# Run main function
main "$@"
