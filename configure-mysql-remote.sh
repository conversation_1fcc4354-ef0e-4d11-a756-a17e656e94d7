#!/bin/bash

echo "🔧 MySQL Remote Connection Configuration"
echo "========================================"

# Note: Run this script on your database server (***********)

echo "1. Checking current MySQL configuration..."

# Check if MySQL is listening on all interfaces
echo "Current MySQL bind address:"
mysql -u root -p -e "SHOW VARIABLES LIKE 'bind_address';" 2>/dev/null || echo "Please run: mysql -u root -p"

echo ""
echo "2. Required MySQL configuration changes:"
echo "----------------------------------------"

cat << 'EOF'
# Edit MySQL configuration file:
sudo nano /etc/mysql/mysql.conf.d/mysqld.cnf

# Find and modify these lines:
bind-address = 0.0.0.0  # Allow connections from any IP
mysqlx-bind-address = 0.0.0.0

# Add these security settings:
max_connections = 50
connect_timeout = 10
wait_timeout = 28800
interactive_timeout = 28800

# Save and restart MySQL:
sudo systemctl restart mysql
EOF

echo ""
echo "3. Required firewall configuration:"
echo "-----------------------------------"

cat << 'EOF'
# Open MySQL port 3306 (be very careful with this):
sudo ufw allow from ************** to any port 3306 comment 'StreamDB Web Server'
sudo ufw allow from YOUR_LOCAL_IP to any port 3306 comment 'Development Access'

# Check firewall status:
sudo ufw status
EOF

echo ""
echo "4. Required MySQL user permissions:"
echo "-----------------------------------"

cat << 'EOF'
# Connect to MySQL as root:
mysql -u root -p

# Grant remote access to your database user:
GRANT ALL PRIVILEGES ON streamdb_database.* TO 'dbadmin_streamdb'@'%' IDENTIFIED BY 'Ohdamn@Ufoundme2';
GRANT ALL PRIVILEGES ON streamdb_database.* TO 'dbadmin_streamdb'@'**************' IDENTIFIED BY 'Ohdamn@Ufoundme2';
FLUSH PRIVILEGES;

# Test the connection:
SELECT User, Host FROM mysql.user WHERE User = 'dbadmin_streamdb';
EOF

echo ""
echo "5. Test remote connection:"
echo "-------------------------"
echo "From your web server (**************), run:"
echo "mysql -h *********** -u dbadmin_streamdb -p streamdb_database"

echo ""
echo "⚠️  SECURITY WARNING:"
echo "====================="
echo "Opening MySQL to remote connections is a security risk."
echo "Make sure to:"
echo "1. Use strong passwords"
echo "2. Limit access to specific IP addresses only"
echo "3. Use SSL encryption"
echo "4. Monitor access logs regularly"
echo "5. Consider using SSH tunneling instead"
