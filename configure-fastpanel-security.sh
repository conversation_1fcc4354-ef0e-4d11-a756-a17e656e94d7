#!/bin/bash

# StreamDB Online - FastPanel + MySQL + Website Security Configuration
# This script configures firewall rules to allow essential services while maintaining security

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROXY_SERVER_IP="*************"
BACKEND_SERVER_IP="***********"

# Function to log messages
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "This script must be run as root (use sudo)"
        exit 1
    fi
}

# Function to backup current UFW rules
backup_current_rules() {
    log_info "Backing up current UFW rules..."
    ufw status numbered > /tmp/ufw_fastpanel_backup_$(date +%Y%m%d_%H%M%S).txt
    log_success "UFW rules backed up to /tmp/"
}

# Function to detect FastPanel ports
detect_fastpanel_ports() {
    log_info "Detecting FastPanel configuration..."
    
    # Common FastPanel ports
    local fastpanel_ports=()
    
    # Check what's actually listening
    log_info "Currently listening ports:"
    netstat -tlnp | grep LISTEN
    
    # Detect FastPanel web interface port
    if netstat -tlnp | grep -q ":5501.*fastpanel"; then
        fastpanel_ports+=(5501)
        log_info "FastPanel web interface detected on port 5501"
    fi
    
    # Check for other FastPanel related ports
    if netstat -tlnp | grep -q ":8080.*nginx"; then
        log_info "FastPanel Nginx detected on port 8080"
    fi
    
    if netstat -tlnp | grep -q ":7777.*nginx"; then
        log_info "FastPanel Nginx detected on port 7777"
    fi
    
    if netstat -tlnp | grep -q ":8888.*nginx"; then
        log_info "FastPanel Nginx detected on port 8888"
    fi
    
    # Check for FTP (if used)
    if netstat -tlnp | grep -q ":21.*proftpd"; then
        log_info "FTP service detected on port 21"
    fi
    
    echo "${fastpanel_ports[@]}"
}

# Function to configure secure FastPanel access
configure_fastpanel_security() {
    log_info "Configuring secure FastPanel access..."
    
    # Reset UFW to clean state but keep essential rules
    log_info "Resetting UFW and configuring essential rules..."
    ufw --force reset
    
    # Set default policies
    ufw default deny incoming
    ufw default allow outgoing
    
    # CRITICAL: Allow SSH (don't lock yourself out!)
    log_info "Allowing SSH access on port 22..."
    ufw allow 22/tcp comment "SSH access"
    
    # Allow proxy server to access web services (HTTP/HTTPS)
    log_info "Allowing proxy server (${PROXY_SERVER_IP}) to access web ports..."
    ufw allow from ${PROXY_SERVER_IP} to any port 80 comment "Proxy HTTP access"
    ufw allow from ${PROXY_SERVER_IP} to any port 443 comment "Proxy HTTPS access"
    
    # Allow localhost connections (for internal services)
    log_info "Allowing localhost connections..."
    ufw allow from 127.0.0.1 comment "Localhost access"
    ufw allow from ::1 comment "IPv6 localhost access"
    
    # FastPanel web interface - SECURE ACCESS ONLY
    log_info "Configuring FastPanel web interface access..."
    
    # Option 1: Allow FastPanel only from specific IP (recommended)
    # Replace YOUR_ADMIN_IP with your actual admin IP address
    # ufw allow from YOUR_ADMIN_IP to any port 5501 comment "FastPanel admin access"
    
    # Option 2: Allow FastPanel from proxy server (if you access via proxy)
    ufw allow from ${PROXY_SERVER_IP} to any port 5501 comment "FastPanel via proxy"
    
    # Option 3: Temporary - Allow from anywhere (LESS SECURE - use only if needed)
    log_warning "Allowing FastPanel access from anywhere (consider restricting to specific IPs)"
    ufw allow 5501/tcp comment "FastPanel web interface"
    
    # FastPanel Nginx ports (for web hosting)
    log_info "Allowing FastPanel Nginx ports for proxy access..."
    ufw allow from ${PROXY_SERVER_IP} to any port 8080 comment "FastPanel Nginx 8080"
    ufw allow from ${PROXY_SERVER_IP} to any port 7777 comment "FastPanel Nginx 7777" 
    ufw allow from ${PROXY_SERVER_IP} to any port 8888 comment "FastPanel Nginx 8888"
    
    # FTP access (if needed) - SECURE
    if netstat -tlnp | grep -q ":21.*proftpd"; then
        log_info "Configuring secure FTP access..."
        # Allow FTP only from specific IPs or proxy
        ufw allow from ${PROXY_SERVER_IP} to any port 21 comment "FTP via proxy"
        # Uncomment next line if you need direct FTP access from your admin IP
        # ufw allow from YOUR_ADMIN_IP to any port 21 comment "FTP admin access"
    fi
    
    # MySQL - LOCALHOST ONLY (most secure)
    log_info "Ensuring MySQL is localhost-only..."
    # MySQL should only be accessible from localhost - no external access
    # This is already secure, just ensuring no rules allow external MySQL access
    
    # Node.js application - LOCALHOST AND PROXY ONLY
    log_info "Configuring Node.js application access..."
    ufw allow from 127.0.0.1 to any port 3001 comment "Node.js localhost"
    ufw allow from ${PROXY_SERVER_IP} to any port 3001 comment "Node.js via proxy"
    
    # Explicitly deny dangerous ports from external access
    log_info "Explicitly denying dangerous ports from external access..."
    ufw deny 3306 comment "Block external MySQL"
    ufw deny 9000 comment "Block webhook port"
    
    # Enable firewall
    log_info "Enabling UFW firewall..."
    ufw --force enable
    
    log_success "FastPanel security configuration completed"
}

# Function to verify FastPanel services
verify_fastpanel_services() {
    log_info "Verifying FastPanel services..."
    
    # Check FastPanel service
    if systemctl is-active --quiet fastpanel 2>/dev/null; then
        log_success "FastPanel service is running"
    else
        log_warning "FastPanel service may not be running"
        log_info "Try: systemctl status fastpanel"
    fi
    
    # Check FastPanel web interface
    if curl -s --connect-timeout 5 http://127.0.0.1:5501 >/dev/null 2>&1; then
        log_success "FastPanel web interface is responding locally"
    else
        log_warning "FastPanel web interface may not be responding"
    fi
    
    # Check MySQL
    if systemctl is-active --quiet mysql 2>/dev/null; then
        log_success "MySQL service is running"
    else
        log_warning "MySQL service may not be running"
    fi
    
    # Check Nginx
    if systemctl is-active --quiet nginx 2>/dev/null; then
        log_success "Nginx service is running"
    else
        log_warning "Nginx service may not be running"
    fi
    
    # Check PM2 processes
    if command -v pm2 >/dev/null 2>&1; then
        log_info "PM2 processes:"
        pm2 list
    fi
}

# Function to show access information
show_access_information() {
    log_info "FastPanel Access Information:"
    echo "============================================"
    
    # Get server's external IP
    external_ip=$(curl -s ifconfig.me 2>/dev/null || echo "***********")
    
    echo ""
    log_info "🌐 FastPanel Web Interface Access:"
    log_info "   Direct: http://${external_ip}:5501"
    log_info "   Via Proxy: http://*************:5501 (if configured)"
    
    echo ""
    log_info "🌐 Website Access:"
    log_info "   Public: https://streamdb.online"
    log_info "   Admin Panel: https://streamdb.online/admin"
    
    echo ""
    log_info "🔒 Security Status:"
    log_info "   ✅ MySQL: Localhost only (secure)"
    log_info "   ✅ Node.js: Proxy + localhost access"
    log_info "   ✅ FastPanel: Accessible for management"
    log_info "   ✅ SSH: Secure access maintained"
    
    echo ""
    log_warning "🔐 Security Recommendations:"
    log_info "   1. Restrict FastPanel access to your admin IP only"
    log_info "   2. Use VPN for FastPanel access if possible"
    log_info "   3. Change default FastPanel port if needed"
    log_info "   4. Enable FastPanel 2FA if available"
}

# Function to create FastPanel-specific verification
create_fastpanel_verification() {
    log_info "Creating FastPanel verification script..."
    
    cat > /root/verify-fastpanel-access.sh << 'EOF'
#!/bin/bash

# FastPanel Access Verification Script

echo "🔍 Verifying FastPanel and services access..."
echo "============================================="

# Test FastPanel web interface
echo ""
echo "Testing FastPanel web interface:"
if curl -s --connect-timeout 10 http://127.0.0.1:5501 >/dev/null 2>&1; then
    echo "✅ FastPanel web interface: Accessible locally"
else
    echo "❌ FastPanel web interface: Not accessible locally"
fi

# Test MySQL
echo ""
echo "Testing MySQL:"
if systemctl is-active --quiet mysql; then
    echo "✅ MySQL service: Running"
    if netstat -tlnp | grep -q "127.0.0.1:3306"; then
        echo "✅ MySQL: Listening on localhost only (secure)"
    else
        echo "⚠️  MySQL: Check listening configuration"
    fi
else
    echo "❌ MySQL service: Not running"
fi

# Test website
echo ""
echo "Testing website access:"
if curl -s --connect-timeout 10 https://streamdb.online >/dev/null 2>&1; then
    echo "✅ Website: Accessible via proxy"
else
    echo "❌ Website: Not accessible via proxy"
fi

# Test Node.js application
echo ""
echo "Testing Node.js application:"
if curl -s --connect-timeout 5 http://127.0.0.1:3001/api/health >/dev/null 2>&1; then
    echo "✅ Node.js application: Running locally"
else
    echo "⚠️  Node.js application: May not be responding"
fi

# Show current firewall status
echo ""
echo "Current firewall rules:"
ufw status numbered

echo ""
echo "🎯 FastPanel Access URLs:"
echo "   FastPanel: http://$(curl -s ifconfig.me):5501"
echo "   Website: https://streamdb.online"
echo "   Admin Panel: https://streamdb.online/admin"
EOF

    chmod +x /root/verify-fastpanel-access.sh
    log_success "FastPanel verification script created at /root/verify-fastpanel-access.sh"
}

# Main execution
main() {
    log_info "🔧 FastPanel + MySQL + Website Security Configuration"
    log_info "===================================================="
    
    # Check prerequisites
    check_root
    
    # Backup and configure
    backup_current_rules
    detect_fastpanel_ports
    configure_fastpanel_security
    verify_fastpanel_services
    create_fastpanel_verification
    show_access_information
    
    echo ""
    log_success "🎉 FastPanel security configuration completed!"
    log_info ""
    log_info "🔍 Next steps:"
    log_info "1. Test FastPanel access: http://$(curl -s ifconfig.me 2>/dev/null || echo '***********'):5501"
    log_info "2. Test website: https://streamdb.online"
    log_info "3. Test admin panel: https://streamdb.online/admin"
    log_info "4. Run verification: ./verify-fastpanel-access.sh"
    log_info ""
    log_warning "⚠️  Security Notes:"
    log_info "   - FastPanel is now accessible (consider IP restrictions)"
    log_info "   - MySQL remains localhost-only (secure)"
    log_info "   - Reverse proxy flow is maintained"
    log_info "   - All essential services are accessible"
}

# Run main function
main "$@"
