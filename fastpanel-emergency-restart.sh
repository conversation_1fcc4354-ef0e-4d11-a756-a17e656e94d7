#!/bin/bash

# StreamDB Online - FastPanel Emergency Restart
# Quick fix to get FastPanel running and accessible from proxy server

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to log messages
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "This script must be run as root (use sudo)"
        exit 1
    fi
}

# Function to stop any existing FastPanel processes
stop_fastpanel() {
    log_info "Stopping any existing FastPanel processes..."
    
    # Kill any existing FastPanel processes
    pkill -f fastpanel 2>/dev/null || log_info "No FastPanel processes to stop"
    
    # Wait for processes to stop
    sleep 3
    
    # Check if any are still running
    if pgrep -f fastpanel >/dev/null; then
        log_warning "Some FastPanel processes still running, force killing..."
        pkill -9 -f fastpanel 2>/dev/null || true
        sleep 2
    fi
    
    log_success "FastPanel processes stopped"
}

# Function to start FastPanel with proper binding
start_fastpanel() {
    log_info "Starting FastPanel with proper configuration..."
    
    # Find FastPanel binary
    local fastpanel_binary=""
    local possible_paths=(
        "/usr/local/fastpanel2/fastpanel"
        "/usr/local/bin/fastpanel"
        "/usr/bin/fastpanel"
        "/opt/fastpanel/bin/fastpanel"
        "/usr/local/fastpanel/bin/fastpanel"
        "/usr/sbin/fastpanel"
    )
    
    for path in "${possible_paths[@]}"; do
        if [ -x "$path" ]; then
            fastpanel_binary="$path"
            log_success "Found FastPanel binary: $path"
            break
        fi
    done
    
    if [ -z "$fastpanel_binary" ]; then
        log_error "FastPanel binary not found!"
        return 1
    fi
    
    # Start FastPanel with proper binding (0.0.0.0 to allow proxy access)
    log_info "Starting FastPanel on 0.0.0.0:5501..."
    
    # Create a startup script for FastPanel
    cat > /tmp/start_fastpanel.sh << EOF
#!/bin/bash
cd /usr/local/fastpanel2/
export FASTPANEL_BIND="0.0.0.0"
export FASTPANEL_PORT="5501"
$fastpanel_binary start --bind 0.0.0.0 --port 5501 &
EOF
    
    chmod +x /tmp/start_fastpanel.sh
    
    # Start FastPanel
    /tmp/start_fastpanel.sh
    
    # Wait for FastPanel to start
    sleep 5
    
    # Verify it's running
    if netstat -tlnp | grep -q ":5501"; then
        log_success "FastPanel started and listening on port 5501"
        netstat -tlnp | grep ":5501"
    else
        log_error "FastPanel failed to start on port 5501"
        return 1
    fi
}

# Function to configure firewall for proxy access
configure_firewall() {
    log_info "Configuring firewall for proxy server access..."
    
    # Allow proxy server to access FastPanel
    ufw allow from ************* to any port 5501 comment "FastPanel proxy access" 2>/dev/null || true
    
    # Allow localhost access
    ufw allow from 127.0.0.1 to any port 5501 comment "FastPanel localhost" 2>/dev/null || true
    
    # Allow the backend IP itself
    local backend_ip=$(hostname -I | awk '{print $1}')
    ufw allow from $backend_ip to any port 5501 comment "FastPanel backend IP" 2>/dev/null || true
    
    log_success "Firewall configured for FastPanel access"
}

# Function to test FastPanel accessibility
test_fastpanel_access() {
    log_info "Testing FastPanel accessibility..."
    
    # Test localhost access
    echo ""
    log_info "Testing localhost access (127.0.0.1:5501):"
    if curl -s --connect-timeout 5 -I http://127.0.0.1:5501 2>/dev/null | head -1; then
        log_success "Localhost access: Working"
    else
        log_error "Localhost access: Failed"
    fi
    
    # Test backend IP access
    echo ""
    local backend_ip=$(hostname -I | awk '{print $1}')
    log_info "Testing backend IP access (${backend_ip}:5501):"
    if curl -s --connect-timeout 5 -I http://${backend_ip}:5501 2>/dev/null | head -1; then
        log_success "Backend IP access: Working"
    else
        log_error "Backend IP access: Failed"
    fi
    
    # Test from proxy server perspective (simulate proxy request)
    echo ""
    log_info "Testing proxy-style access:"
    if curl -s --connect-timeout 5 -H "X-Forwarded-For: *************" -I http://${backend_ip}:5501 2>/dev/null | head -1; then
        log_success "Proxy-style access: Working"
    else
        log_warning "Proxy-style access: May need additional configuration"
    fi
}

# Function to create persistent startup configuration
create_persistent_config() {
    log_info "Creating persistent FastPanel configuration..."
    
    # Create systemd service for FastPanel (if systemd is available)
    if command -v systemctl >/dev/null 2>&1; then
        cat > /etc/systemd/system/fastpanel-custom.service << 'EOF'
[Unit]
Description=FastPanel Custom Service
After=network.target

[Service]
Type=forking
User=root
WorkingDirectory=/usr/local/fastpanel2/
Environment=FASTPANEL_BIND=0.0.0.0
Environment=FASTPANEL_PORT=5501
ExecStart=/usr/local/fastpanel2/fastpanel start --bind 0.0.0.0 --port 5501
ExecStop=/usr/bin/pkill -f fastpanel
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF
        
        systemctl daemon-reload
        systemctl enable fastpanel-custom
        log_success "Systemd service created and enabled"
    fi
    
    # Create startup script in rc.local as backup
    if [ -f "/etc/rc.local" ]; then
        # Remove any existing FastPanel entries
        sed -i '/fastpanel/d' /etc/rc.local
        
        # Add new FastPanel startup before exit 0
        sed -i '/^exit 0/i # Start FastPanel for StreamDB\n/usr/local/fastpanel2/fastpanel start --bind 0.0.0.0 --port 5501 &' /etc/rc.local
        
        log_success "Added FastPanel to rc.local startup"
    fi
}

# Function to show final status
show_final_status() {
    log_info "📋 FastPanel Status Report"
    echo "=========================="
    
    echo ""
    log_info "Process status:"
    if pgrep -f fastpanel >/dev/null; then
        log_success "FastPanel processes are running"
        ps aux | grep fastpanel | grep -v grep
    else
        log_error "No FastPanel processes found"
    fi
    
    echo ""
    log_info "Port 5501 status:"
    if netstat -tlnp | grep -q ":5501"; then
        log_success "Port 5501 is listening"
        netstat -tlnp | grep ":5501"
    else
        log_error "Port 5501 is not listening"
    fi
    
    echo ""
    log_info "Accessibility test:"
    if curl -s --connect-timeout 5 http://127.0.0.1:5501 >/dev/null 2>&1; then
        log_success "FastPanel is accessible locally"
    else
        log_error "FastPanel is NOT accessible locally"
    fi
    
    local backend_ip=$(hostname -I | awk '{print $1}')
    if curl -s --connect-timeout 5 http://${backend_ip}:5501 >/dev/null 2>&1; then
        log_success "FastPanel is accessible on backend IP"
    else
        log_error "FastPanel is NOT accessible on backend IP"
    fi
    
    echo ""
    log_info "🎯 Next steps:"
    log_info "1. Test FastPanel access from proxy server"
    log_info "2. Test external access: https://fastpanel.streamdb.online/"
    log_info "3. Verify main website still works: https://streamdb.online/"
    
    echo ""
    log_info "🌐 Expected working URLs:"
    log_info "   FastPanel: https://fastpanel.streamdb.online/"
    log_info "   Website: https://streamdb.online/"
    log_info "   Admin Panel: https://streamdb.online/admin"
}

# Main execution
main() {
    log_info "🚨 FastPanel Emergency Restart"
    log_info "=============================="
    
    # Check prerequisites
    check_root
    
    # Execute emergency restart sequence
    stop_fastpanel
    echo ""
    start_fastpanel
    echo ""
    configure_firewall
    echo ""
    test_fastpanel_access
    echo ""
    create_persistent_config
    echo ""
    show_final_status
    
    echo ""
    log_success "🎉 FastPanel emergency restart completed!"
    log_info ""
    log_warning "⚠️  Important notes:"
    log_info "   - FastPanel is now running on 0.0.0.0:5501 (accessible to proxy)"
    log_info "   - Firewall allows proxy server (*************) access"
    log_info "   - Persistent startup configuration created"
    log_info "   - Test external access after DNS propagation"
}

# Run main function
main "$@"
