#!/bin/bash

# StreamDB Online - Fix Proxy Server SSL Issues
# This script fixes the SSL configuration conflicts and FastPanel access

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to log messages
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "This script must be run as root (use sudo)"
        exit 1
    fi
}

# Function to backup current configuration
backup_nginx_config() {
    log_info "Backing up current Nginx configuration..."
    
    # Create backup directory
    mkdir -p /root/nginx-backup-$(date +%Y%m%d_%H%M%S)
    
    # Backup sites-enabled
    cp -r /etc/nginx/sites-enabled /root/nginx-backup-$(date +%Y%m%d_%H%M%S)/
    cp -r /etc/nginx/sites-available /root/nginx-backup-$(date +%Y%m%d_%H%M%S)/
    
    log_success "Nginx configuration backed up"
}

# Function to fix SSL configuration conflicts
fix_ssl_conflicts() {
    log_info "Fixing SSL configuration conflicts..."
    
    # Remove the problematic FastPanel configuration temporarily
    rm -f /etc/nginx/sites-enabled/fastpanel.streamdb.online
    
    # Create a corrected FastPanel configuration without SSL conflicts
    cat > /etc/nginx/sites-available/fastpanel.streamdb.online << 'EOF'
# FastPanel Subdomain Proxy Configuration
server {
    listen 80;
    server_name fastpanel.streamdb.online;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    
    # Proxy to backend FastPanel
    location / {
        proxy_pass http://***********:5501;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # FastPanel specific headers
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Server $host;
        
        # Timeouts for FastPanel
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # Buffer settings
        proxy_buffering off;
        proxy_request_buffering off;
    }
    
    # Health check endpoint
    location /health {
        access_log off;
        return 200 "FastPanel Proxy OK\n";
        add_header Content-Type text/plain;
    }
}
EOF

    log_success "FastPanel configuration updated (HTTP only for now)"
}

# Function to check existing SSL configuration
check_existing_ssl() {
    log_info "Checking existing SSL configuration..."
    
    # Check if there are existing SSL certificates
    if [ -f "/etc/letsencrypt/live/streamdb.online/fullchain.pem" ]; then
        log_success "Found existing SSL certificates for streamdb.online"
        
        # Add HTTPS configuration for FastPanel using existing certificates
        cat >> /etc/nginx/sites-available/fastpanel.streamdb.online << 'EOF'

# HTTPS Configuration using existing SSL certificates
server {
    listen 443 ssl http2;
    server_name fastpanel.streamdb.online;
    
    # Use existing SSL certificates
    ssl_certificate /etc/letsencrypt/live/streamdb.online/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/streamdb.online/privkey.pem;
    
    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # Proxy to backend FastPanel
    location / {
        proxy_pass http://***********:5501;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # FastPanel specific headers
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Server $host;
        
        # Timeouts for FastPanel
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # Buffer settings
        proxy_buffering off;
        proxy_request_buffering off;
    }
    
    # Health check endpoint
    location /health {
        access_log off;
        return 200 "FastPanel Proxy OK (HTTPS)\n";
        add_header Content-Type text/plain;
    }
}
EOF
        log_success "HTTPS configuration added using existing certificates"
        
    else
        log_warning "No existing SSL certificates found"
        log_info "FastPanel will work on HTTP only for now"
        log_info "You can add SSL certificates later if needed"
    fi
}

# Function to enable and test configuration
enable_and_test_config() {
    log_info "Enabling FastPanel configuration..."
    
    # Enable the site
    ln -sf /etc/nginx/sites-available/fastpanel.streamdb.online /etc/nginx/sites-enabled/
    
    # Test Nginx configuration
    log_info "Testing Nginx configuration..."
    if nginx -t 2>/dev/null; then
        log_success "Nginx configuration is valid"
        
        # Reload Nginx
        log_info "Reloading Nginx..."
        systemctl reload nginx
        log_success "Nginx reloaded successfully"
        
    else
        log_error "Nginx configuration test failed"
        log_info "Nginx error details:"
        nginx -t
        return 1
    fi
}

# Function to test FastPanel proxy
test_fastpanel_proxy() {
    log_info "Testing FastPanel proxy..."
    
    # Wait a moment for Nginx to fully reload
    sleep 2
    
    # Test HTTP health check
    log_info "Testing HTTP health check..."
    if curl -s --connect-timeout 10 -H "Host: fastpanel.streamdb.online" http://localhost/health | grep -q "FastPanel Proxy OK"; then
        log_success "HTTP health check: OK"
    else
        log_warning "HTTP health check: May not be working"
    fi
    
    # Test HTTPS health check (if SSL is configured)
    if [ -f "/etc/letsencrypt/live/streamdb.online/fullchain.pem" ]; then
        log_info "Testing HTTPS health check..."
        if curl -s --connect-timeout 10 -k -H "Host: fastpanel.streamdb.online" https://localhost/health | grep -q "FastPanel Proxy OK"; then
            log_success "HTTPS health check: OK"
        else
            log_warning "HTTPS health check: May not be working"
        fi
    fi
    
    # Test external access (if possible)
    log_info "Testing external access..."
    if curl -s --connect-timeout 15 http://fastpanel.streamdb.online/health 2>/dev/null | grep -q "FastPanel Proxy OK"; then
        log_success "External HTTP access: OK"
    else
        log_warning "External HTTP access: Check DNS propagation and backend server"
    fi
}

# Function to show status and next steps
show_status_and_next_steps() {
    log_info "📋 Configuration Status"
    echo "======================="
    
    echo ""
    log_info "Current Nginx sites enabled:"
    ls -la /etc/nginx/sites-enabled/
    
    echo ""
    log_info "FastPanel proxy configuration:"
    if [ -f "/etc/nginx/sites-enabled/fastpanel.streamdb.online" ]; then
        log_success "FastPanel proxy: Enabled"
    else
        log_error "FastPanel proxy: Not enabled"
    fi
    
    echo ""
    log_info "SSL certificate status:"
    if [ -f "/etc/letsencrypt/live/streamdb.online/fullchain.pem" ]; then
        log_success "SSL certificates: Available"
        log_info "FastPanel will work on both HTTP and HTTPS"
    else
        log_warning "SSL certificates: Not found"
        log_info "FastPanel will work on HTTP only"
    fi
    
    echo ""
    log_info "🔍 Next steps:"
    log_info "1. Ensure backend server (***********) FastPanel is running on port 5501"
    log_info "2. Wait for DNS propagation (5-30 minutes)"
    log_info "3. Test access: http://fastpanel.streamdb.online/"
    log_info "4. If HTTPS is needed, configure SSL certificates"
    
    echo ""
    log_info "🌐 Access URLs:"
    log_info "   FastPanel: http://fastpanel.streamdb.online/"
    if [ -f "/etc/letsencrypt/live/streamdb.online/fullchain.pem" ]; then
        log_info "   FastPanel (HTTPS): https://fastpanel.streamdb.online/"
    fi
    log_info "   Main website: https://streamdb.online/"
    log_info "   Admin panel: https://streamdb.online/admin"
}

# Main execution
main() {
    log_info "🔧 Fixing Proxy Server SSL Issues"
    log_info "================================="
    
    # Check prerequisites
    check_root
    
    # Run fixes
    backup_nginx_config
    fix_ssl_conflicts
    check_existing_ssl
    enable_and_test_config
    test_fastpanel_proxy
    show_status_and_next_steps
    
    echo ""
    log_success "🎉 Proxy server SSL issues fixed!"
    log_info ""
    log_warning "⚠️  Important reminders:"
    log_info "   - Ensure DNS points fastpanel.streamdb.online to this proxy server"
    log_info "   - Ensure backend server FastPanel is running and accessible"
    log_info "   - Test from external network, not localhost"
    log_info "   - Check Cloudflare SSL settings if using Cloudflare"
}

# Run main function
main "$@"
