#!/bin/bash

# StreamDB Online - Fix FastPanel Proxy Access
# This script configures FastPanel to accept connections from proxy server

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROXY_SERVER_IP="*************"
BACKEND_SERVER_IP="***********"

# Function to log messages
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "This script must be run as root (use sudo)"
        exit 1
    fi
}

# Function to find FastPanel configuration
find_fastpanel_config() {
    log_info "Finding FastPanel configuration files..."
    
    # Common FastPanel config locations
    local config_paths=(
        "/etc/fastpanel/fastpanel.conf"
        "/etc/fastpanel2/fastpanel.conf"
        "/usr/local/fastpanel/etc/fastpanel.conf"
        "/opt/fastpanel/etc/fastpanel.conf"
        "/etc/fastpanel.conf"
    )
    
    for config_path in "${config_paths[@]}"; do
        if [ -f "$config_path" ]; then
            log_success "Found FastPanel config: $config_path"
            echo "$config_path"
            return 0
        fi
    done
    
    log_warning "FastPanel config file not found in standard locations"
    log_info "Searching for FastPanel config files..."
    find /etc /usr/local /opt -name "*fastpanel*" -type f 2>/dev/null | grep -E "\.(conf|cfg|ini)$" | head -5
    
    return 1
}

# Function to configure FastPanel to listen on all interfaces
configure_fastpanel_listening() {
    log_info "Configuring FastPanel to accept proxy connections..."
    
    # Find FastPanel config
    local config_file
    config_file=$(find_fastpanel_config)
    
    if [ -n "$config_file" ] && [ -f "$config_file" ]; then
        log_info "Backing up FastPanel configuration..."
        cp "$config_file" "${config_file}.backup.$(date +%Y%m%d_%H%M%S)"
        
        log_info "Current FastPanel configuration:"
        grep -E "(listen|bind|host|port)" "$config_file" 2>/dev/null || log_info "No listen/bind configuration found"
        
        # Check if configuration needs updating
        if grep -q "127.0.0.1" "$config_file" 2>/dev/null; then
            log_info "Updating FastPanel to listen on all interfaces..."
            sed -i 's/127\.0\.0\.1/0.0.0.0/g' "$config_file"
            log_success "FastPanel configuration updated"
        else
            log_info "FastPanel configuration may already be correct"
        fi
    else
        log_warning "Could not find FastPanel configuration file"
        log_info "FastPanel may be using default settings"
    fi
}

# Function to restart FastPanel service
restart_fastpanel() {
    log_info "Restarting FastPanel service..."
    
    # Try different service names
    local service_names=("fastpanel" "fastpanel2" "fp2")
    local service_restarted=false
    
    for service_name in "${service_names[@]}"; do
        if systemctl list-units --type=service | grep -q "$service_name"; then
            log_info "Found service: $service_name"
            systemctl restart "$service_name" || log_warning "Could not restart $service_name"
            service_restarted=true
            break
        fi
    done
    
    if [ "$service_restarted" = false ]; then
        log_warning "FastPanel service not found in systemctl"
        log_info "Trying to find FastPanel process..."
        
        # Find FastPanel process and restart it
        local fastpanel_pid
        fastpanel_pid=$(pgrep -f fastpanel | head -1)
        
        if [ -n "$fastpanel_pid" ]; then
            log_info "Found FastPanel process: $fastpanel_pid"
            log_info "Sending restart signal..."
            kill -HUP "$fastpanel_pid" 2>/dev/null || log_warning "Could not restart FastPanel process"
        else
            log_warning "FastPanel process not found"
        fi
    fi
    
    # Wait a moment for restart
    sleep 3
}

# Function to verify FastPanel is listening correctly
verify_fastpanel_listening() {
    log_info "Verifying FastPanel listening configuration..."
    
    # Check what's listening on port 5501
    log_info "Current port 5501 listeners:"
    netstat -tlnp | grep ":5501" || log_warning "No process listening on port 5501"
    
    # Test local access
    if curl -s --connect-timeout 5 http://127.0.0.1:5501 >/dev/null 2>&1; then
        log_success "FastPanel responds on localhost"
    else
        log_warning "FastPanel not responding on localhost"
    fi
    
    # Test access on backend IP
    if curl -s --connect-timeout 5 http://${BACKEND_SERVER_IP}:5501 >/dev/null 2>&1; then
        log_success "FastPanel responds on backend IP (good for proxy)"
    else
        log_warning "FastPanel not responding on backend IP"
        log_info "This may prevent proxy access"
    fi
}

# Function to create alternative solution using nginx proxy
create_nginx_proxy_solution() {
    log_info "Creating alternative Nginx proxy solution..."
    
    # Create a local Nginx configuration to proxy FastPanel
    cat > /etc/nginx/sites-available/fastpanel-proxy << EOF
# FastPanel Local Proxy Configuration
# This allows proxy server to access FastPanel via port 80/443

server {
    listen 80;
    server_name fastpanel.streamdb.online;
    
    # Proxy to local FastPanel
    location / {
        proxy_pass http://127.0.0.1:5501;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        
        # FastPanel specific headers
        proxy_set_header X-Forwarded-Host \$host;
        proxy_set_header X-Forwarded-Server \$host;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}

server {
    listen 443 ssl http2;
    server_name fastpanel.streamdb.online;
    
    # Use existing SSL certificates if available
    # ssl_certificate /path/to/ssl/cert.pem;
    # ssl_certificate_key /path/to/ssl/key.pem;
    
    # For now, redirect to HTTP (update when SSL is configured)
    return 301 http://\$server_name\$request_uri;
}
EOF

    # Enable the site
    if [ -d "/etc/nginx/sites-enabled" ]; then
        ln -sf /etc/nginx/sites-available/fastpanel-proxy /etc/nginx/sites-enabled/
        log_success "Nginx proxy configuration created and enabled"
    else
        log_info "Nginx sites-enabled directory not found"
        log_info "Configuration created at: /etc/nginx/sites-available/fastpanel-proxy"
    fi
    
    # Test Nginx configuration
    if nginx -t 2>/dev/null; then
        log_success "Nginx configuration is valid"
        systemctl reload nginx
        log_success "Nginx reloaded"
    else
        log_warning "Nginx configuration test failed"
        log_info "Manual configuration may be needed"
    fi
}

# Function to show updated proxy server configuration
show_updated_proxy_config() {
    log_info "Updated Proxy Server Configuration:"
    echo "===================================="
    
    echo ""
    log_info "🔧 Option 1: If FastPanel now listens on all interfaces"
    log_info "Use the previous configuration (proxy to ***********:5501)"
    
    echo ""
    log_info "🔧 Option 2: If FastPanel still localhost-only (recommended)"
    log_info "Update your proxy server to use port 80/443:"
    echo ""
    cat << 'EOF'
# Updated proxy server configuration:

server {
    listen 80;
    listen 443 ssl http2;
    server_name fastpanel.streamdb.online;
    
    # SSL configuration (if using HTTPS)
    # ssl_certificate /path/to/your/ssl/cert.pem;
    # ssl_certificate_key /path/to/your/ssl/key.pem;
    
    # Proxy to backend via HTTP/HTTPS (not port 5501)
    location / {
        proxy_pass http://***********:80;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host fastpanel.streamdb.online;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
EOF
}

# Function to create final verification
create_final_verification() {
    log_info "Creating final verification script..."
    
    cat > /root/verify-fastpanel-final.sh << 'EOF'
#!/bin/bash

echo "🔍 Final FastPanel Access Verification"
echo "======================================"

BACKEND_IP="***********"

# Test FastPanel on different interfaces
echo ""
echo "Testing FastPanel access:"

if curl -s --connect-timeout 5 http://127.0.0.1:5501 >/dev/null 2>&1; then
    echo "✅ FastPanel: Responds on localhost"
else
    echo "❌ FastPanel: Not responding on localhost"
fi

if curl -s --connect-timeout 5 http://${BACKEND_IP}:5501 >/dev/null 2>&1; then
    echo "✅ FastPanel: Responds on backend IP (proxy can access)"
else
    echo "⚠️  FastPanel: Not responding on backend IP (proxy may not work)"
fi

if curl -s --connect-timeout 5 http://${BACKEND_IP}:80 >/dev/null 2>&1; then
    echo "✅ HTTP: Responds on backend IP (alternative proxy method)"
else
    echo "⚠️  HTTP: Not responding on backend IP"
fi

# Test subdomain access
echo ""
echo "Testing subdomain access:"
if curl -s --connect-timeout 10 https://fastpanel.streamdb.online >/dev/null 2>&1; then
    echo "✅ FastPanel subdomain: Working (HTTPS)"
elif curl -s --connect-timeout 10 http://fastpanel.streamdb.online >/dev/null 2>&1; then
    echo "✅ FastPanel subdomain: Working (HTTP)"
else
    echo "⚠️  FastPanel subdomain: Not accessible (check proxy configuration)"
fi

# Test main website
echo ""
echo "Testing main website:"
if curl -s --connect-timeout 10 https://streamdb.online >/dev/null 2>&1; then
    echo "✅ Main website: Accessible"
else
    echo "❌ Main website: Not accessible"
fi

# Show listening ports
echo ""
echo "Current listening configuration:"
echo "Port 5501 (FastPanel):"
netstat -tlnp | grep ":5501" || echo "  No listeners on port 5501"
echo "Port 80 (HTTP):"
netstat -tlnp | grep ":80" || echo "  No listeners on port 80"

echo ""
echo "🎯 Access URLs to test:"
echo "   FastPanel: https://fastpanel.streamdb.online/"
echo "   Website: https://streamdb.online"
echo "   Admin Panel: https://streamdb.online/admin"
EOF

    chmod +x /root/verify-fastpanel-final.sh
    log_success "Final verification script created"
}

# Main execution
main() {
    log_info "🔧 Fixing FastPanel Proxy Access"
    log_info "================================"
    
    # Check prerequisites
    check_root
    
    # Configure FastPanel
    configure_fastpanel_listening
    restart_fastpanel
    verify_fastpanel_listening
    create_nginx_proxy_solution
    create_final_verification
    show_updated_proxy_config
    
    echo ""
    log_success "🎉 FastPanel proxy access configuration completed!"
    log_info ""
    log_info "🔍 Next steps:"
    log_info "1. Test FastPanel: https://fastpanel.streamdb.online/"
    log_info "2. If not working, update proxy server config (see above)"
    log_info "3. Run verification: ./verify-fastpanel-final.sh"
    log_info "4. Test website: https://streamdb.online"
    log_info ""
    log_warning "⚠️  If FastPanel subdomain doesn't work:"
    log_info "   - Check proxy server Nginx configuration"
    log_info "   - Try the alternative configuration (Option 2 above)"
    log_info "   - Verify DNS points to proxy server"
}

# Run main function
main "$@"
