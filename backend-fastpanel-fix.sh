#!/bin/bash

# StreamDB Online - Backend FastPanel Fix
# This script ensures FastPanel is running and accessible on the backend server

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to log messages
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "This script must be run as root (use sudo)"
        exit 1
    fi
}

# Function to diagnose FastPanel status
diagnose_fastpanel() {
    log_info "🔍 Diagnosing FastPanel Status"
    echo "=============================="
    
    # Check what's listening on port 5501
    echo ""
    log_info "Port 5501 listeners:"
    if netstat -tlnp | grep ":5501"; then
        log_success "Something is listening on port 5501"
        netstat -tlnp | grep ":5501"
    else
        log_error "Nothing listening on port 5501"
    fi
    
    # Check FastPanel process
    echo ""
    log_info "FastPanel processes:"
    if ps aux | grep -i fastpanel | grep -v grep; then
        log_success "FastPanel processes found"
    else
        log_warning "No FastPanel processes found"
    fi
    
    # Check FastPanel service
    echo ""
    log_info "FastPanel service status:"
    if systemctl is-active --quiet fastpanel 2>/dev/null; then
        log_success "FastPanel service is active"
        systemctl status fastpanel --no-pager -l
    else
        log_warning "FastPanel service is not active or not found"
    fi
    
    # Test local FastPanel access
    echo ""
    log_info "Testing local FastPanel access:"
    if curl -s --connect-timeout 5 http://127.0.0.1:5501 >/dev/null 2>&1; then
        log_success "FastPanel responds on localhost"
    else
        log_error "FastPanel NOT responding on localhost"
    fi
    
    # Test FastPanel on backend IP
    echo ""
    log_info "Testing FastPanel on backend IP:"
    local backend_ip=$(hostname -I | awk '{print $1}')
    if curl -s --connect-timeout 5 http://${backend_ip}:5501 >/dev/null 2>&1; then
        log_success "FastPanel responds on backend IP: $backend_ip"
    else
        log_error "FastPanel NOT responding on backend IP: $backend_ip"
    fi
}

# Function to fix FastPanel listening configuration
fix_fastpanel_listening() {
    log_info "🔧 Fixing FastPanel Configuration"
    echo "=================================="
    
    # Find FastPanel binary
    local fastpanel_binary=""
    local possible_paths=(
        "/usr/local/bin/fastpanel"
        "/usr/bin/fastpanel"
        "/opt/fastpanel/bin/fastpanel"
        "/usr/local/fastpanel/bin/fastpanel"
        "/usr/sbin/fastpanel"
    )
    
    for path in "${possible_paths[@]}"; do
        if [ -x "$path" ]; then
            fastpanel_binary="$path"
            log_success "Found FastPanel binary: $path"
            break
        fi
    done
    
    # Stop any existing FastPanel processes
    log_info "Stopping existing FastPanel processes..."
    pkill -f fastpanel 2>/dev/null || log_info "No FastPanel processes to stop"
    sleep 2
    
    # Try to start FastPanel service
    log_info "Starting FastPanel service..."
    if systemctl start fastpanel 2>/dev/null; then
        log_success "FastPanel service started"
        sleep 3
    else
        log_warning "Could not start FastPanel via systemctl"
        
        # Try to start FastPanel manually if binary found
        if [ -n "$fastpanel_binary" ]; then
            log_info "Starting FastPanel manually..."
            nohup "$fastpanel_binary" --bind 0.0.0.0 --port 5501 >/dev/null 2>&1 &
            sleep 3
            log_info "FastPanel started manually"
        else
            log_error "FastPanel binary not found"
            return 1
        fi
    fi
    
    # Verify FastPanel is now running
    if netstat -tlnp | grep -q ":5501"; then
        log_success "FastPanel is now listening on port 5501"
        netstat -tlnp | grep ":5501"
    else
        log_error "FastPanel still not listening on port 5501"
        return 1
    fi
}

# Function to ensure firewall allows proxy access
configure_firewall_for_proxy() {
    log_info "🔧 Configuring Firewall for Proxy Access"
    echo "========================================="
    
    # Ensure UFW allows proxy server to access FastPanel
    ufw allow from ************* to any port 5501 comment "FastPanel via proxy"
    ufw allow from 127.0.0.1 to any port 5501 comment "FastPanel localhost"
    
    log_success "Firewall configured for proxy access"
    
    # Show relevant firewall rules
    log_info "Relevant firewall rules:"
    ufw status numbered | grep -E "(5501|*************)" || log_info "No specific FastPanel rules found"
}

# Function to test connectivity
test_connectivity() {
    log_info "🔍 Testing Connectivity"
    echo "======================="
    
    # Test local access
    echo ""
    log_info "Testing local FastPanel access (127.0.0.1:5501):"
    if curl -s --connect-timeout 5 -I http://127.0.0.1:5501 2>/dev/null; then
        log_success "Local FastPanel: Accessible"
        curl -s --connect-timeout 5 -I http://127.0.0.1:5501 | head -3
    else
        log_error "Local FastPanel: Not accessible"
    fi
    
    # Test backend IP access
    echo ""
    local backend_ip=$(hostname -I | awk '{print $1}')
    log_info "Testing FastPanel on backend IP (${backend_ip}:5501):"
    if curl -s --connect-timeout 5 -I http://${backend_ip}:5501 2>/dev/null; then
        log_success "Backend FastPanel: Accessible"
        curl -s --connect-timeout 5 -I http://${backend_ip}:5501 | head -3
    else
        log_error "Backend FastPanel: Not accessible"
    fi
    
    # Test main website
    echo ""
    log_info "Testing main website:"
    if curl -s --connect-timeout 10 https://streamdb.online >/dev/null 2>&1; then
        log_success "Main website: Accessible"
    else
        log_warning "Main website: May not be accessible"
    fi
    
    # Test admin panel
    echo ""
    log_info "Testing admin panel:"
    if curl -s --connect-timeout 10 https://streamdb.online/admin >/dev/null 2>&1; then
        log_success "Admin panel: Accessible"
    else
        log_warning "Admin panel: May not be accessible"
    fi
}

# Function to create FastPanel startup script
create_fastpanel_startup() {
    log_info "📝 Creating FastPanel Startup Script"
    echo "===================================="
    
    cat > /usr/local/bin/start-fastpanel.sh << 'EOF'
#!/bin/bash

# FastPanel Startup Script
# Ensures FastPanel is running and accessible

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m'

log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

# Check if FastPanel is already running
if netstat -tlnp | grep -q ":5501"; then
    log_success "FastPanel is already running"
    exit 0
fi

# Try to start FastPanel service
if systemctl start fastpanel 2>/dev/null; then
    log_success "FastPanel service started"
    exit 0
fi

# Try to find and start FastPanel binary
FASTPANEL_PATHS=(
    "/usr/local/bin/fastpanel"
    "/usr/bin/fastpanel"
    "/opt/fastpanel/bin/fastpanel"
    "/usr/local/fastpanel/bin/fastpanel"
    "/usr/sbin/fastpanel"
)

for path in "${FASTPANEL_PATHS[@]}"; do
    if [ -x "$path" ]; then
        echo "Starting FastPanel from: $path"
        nohup "$path" --bind 0.0.0.0 --port 5501 >/dev/null 2>&1 &
        sleep 3
        
        if netstat -tlnp | grep -q ":5501"; then
            log_success "FastPanel started successfully"
            exit 0
        fi
    fi
done

log_error "Could not start FastPanel"
exit 1
EOF

    chmod +x /usr/local/bin/start-fastpanel.sh
    log_success "FastPanel startup script created: /usr/local/bin/start-fastpanel.sh"
}

# Function to show final status
show_final_status() {
    log_info "📋 Final Status Report"
    echo "====================="
    
    echo ""
    log_info "FastPanel listening status:"
    if netstat -tlnp | grep ":5501"; then
        log_success "FastPanel is listening on port 5501"
        netstat -tlnp | grep ":5501"
    else
        log_error "FastPanel is NOT listening on port 5501"
    fi
    
    echo ""
    log_info "FastPanel accessibility:"
    if curl -s --connect-timeout 5 http://127.0.0.1:5501 >/dev/null 2>&1; then
        log_success "FastPanel accessible locally"
    else
        log_error "FastPanel NOT accessible locally"
    fi
    
    local backend_ip=$(hostname -I | awk '{print $1}')
    if curl -s --connect-timeout 5 http://${backend_ip}:5501 >/dev/null 2>&1; then
        log_success "FastPanel accessible on backend IP"
    else
        log_error "FastPanel NOT accessible on backend IP"
    fi
    
    echo ""
    log_info "🎯 Next steps:"
    log_info "1. Ensure proxy server is configured correctly"
    log_info "2. Test FastPanel access: https://fastpanel.streamdb.online/"
    log_info "3. If issues persist, run: /usr/local/bin/start-fastpanel.sh"
    
    echo ""
    log_info "🌐 Expected access URLs:"
    log_info "   FastPanel: https://fastpanel.streamdb.online/"
    log_info "   Website: https://streamdb.online/"
    log_info "   Admin Panel: https://streamdb.online/admin"
}

# Main execution
main() {
    log_info "🔧 Backend FastPanel Fix"
    log_info "========================"
    
    # Check prerequisites
    check_root
    
    # Run diagnosis and fixes
    diagnose_fastpanel
    echo ""
    fix_fastpanel_listening
    echo ""
    configure_firewall_for_proxy
    echo ""
    test_connectivity
    echo ""
    create_fastpanel_startup
    echo ""
    show_final_status
    
    echo ""
    log_success "🎉 Backend FastPanel configuration completed!"
    log_info ""
    log_warning "⚠️  Important notes:"
    log_info "   - FastPanel should now be accessible to proxy server"
    log_info "   - Firewall allows proxy server (*************) access"
    log_info "   - Main website and admin panel functionality preserved"
    log_info "   - Mobile responsiveness and theme maintained"
}

# Run main function
main "$@"
