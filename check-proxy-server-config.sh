#!/bin/bash

# StreamDB Online - Proxy Server Configuration Checker
# This script helps check and configure the proxy server for FastPanel subdomain

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROXY_SERVER_IP="*************"
BACKEND_SERVER_IP="***********"
FASTPANEL_SUBDOMAIN="fastpanel.streamdb.online"
MAIN_DOMAIN="streamdb.online"

# Function to log messages
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to check DNS configuration
check_dns_configuration() {
    log_info "🔍 Checking DNS Configuration"
    echo "============================="
    
    echo ""
    log_info "Checking where domains point:"
    
    # Check main domain
    echo ""
    log_info "Main domain (${MAIN_DOMAIN}):"
    if nslookup ${MAIN_DOMAIN} >/dev/null 2>&1; then
        local main_ip=$(nslookup ${MAIN_DOMAIN} | grep -A1 "Name:" | tail -1 | awk '{print $2}' 2>/dev/null || echo "Unknown")
        if [ "$main_ip" = "$PROXY_SERVER_IP" ]; then
            log_success "Points to proxy server: $main_ip ✅"
        else
            log_warning "Points to: $main_ip (should be $PROXY_SERVER_IP)"
        fi
    else
        log_error "Domain does not resolve"
    fi
    
    # Check FastPanel subdomain
    echo ""
    log_info "FastPanel subdomain (${FASTPANEL_SUBDOMAIN}):"
    if nslookup ${FASTPANEL_SUBDOMAIN} >/dev/null 2>&1; then
        local fastpanel_ip=$(nslookup ${FASTPANEL_SUBDOMAIN} | grep -A1 "Name:" | tail -1 | awk '{print $2}' 2>/dev/null || echo "Unknown")
        if [ "$fastpanel_ip" = "$PROXY_SERVER_IP" ]; then
            log_success "Points to proxy server: $fastpanel_ip ✅"
        elif [ "$fastpanel_ip" = "$BACKEND_SERVER_IP" ]; then
            log_error "Points to backend server: $fastpanel_ip ❌"
            log_warning "This bypasses your proxy! Should point to $PROXY_SERVER_IP"
        else
            log_warning "Points to: $fastpanel_ip (should be $PROXY_SERVER_IP)"
        fi
    else
        log_error "Subdomain does not resolve"
    fi
}

# Function to test proxy server connectivity
test_proxy_server_connectivity() {
    log_info "🔍 Testing Proxy Server Connectivity"
    echo "===================================="
    
    echo ""
    log_info "Testing connection to proxy server (${PROXY_SERVER_IP}):"
    
    # Test HTTP
    if curl -s --connect-timeout 10 http://${PROXY_SERVER_IP} >/dev/null 2>&1; then
        log_success "HTTP connection to proxy server: OK"
    else
        log_error "HTTP connection to proxy server: FAILED"
    fi
    
    # Test HTTPS
    if curl -s --connect-timeout 10 https://${PROXY_SERVER_IP} >/dev/null 2>&1; then
        log_success "HTTPS connection to proxy server: OK"
    else
        log_warning "HTTPS connection to proxy server: May not be configured"
    fi
    
    # Test main domain through proxy
    echo ""
    log_info "Testing main domain through proxy:"
    if curl -s --connect-timeout 10 https://${MAIN_DOMAIN} >/dev/null 2>&1; then
        log_success "Main domain accessible: OK"
    else
        log_error "Main domain not accessible"
    fi
    
    # Test FastPanel subdomain
    echo ""
    log_info "Testing FastPanel subdomain:"
    if curl -s --connect-timeout 10 https://${FASTPANEL_SUBDOMAIN} >/dev/null 2>&1; then
        log_success "FastPanel subdomain accessible: OK"
    elif curl -s --connect-timeout 10 http://${FASTPANEL_SUBDOMAIN} >/dev/null 2>&1; then
        log_warning "FastPanel subdomain accessible via HTTP only"
    else
        log_error "FastPanel subdomain not accessible"
    fi
}

# Function to generate proxy server configuration
generate_proxy_server_config() {
    log_info "📋 Proxy Server Configuration Required"
    echo "======================================"
    
    echo ""
    log_info "🔧 SSH to your proxy server (*************) and add this configuration:"
    echo ""
    
    cat << 'EOF'
# ============================================================================
# NGINX CONFIGURATION FOR PROXY SERVER (*************)
# ============================================================================

# Add this to your Nginx configuration on the proxy server:
# File: /etc/nginx/sites-available/fastpanel.streamdb.online
# or add to existing configuration file

# FastPanel Subdomain Configuration
server {
    listen 80;
    server_name fastpanel.streamdb.online;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    
    # Proxy to backend FastPanel
    location / {
        proxy_pass http://***********:5501;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # FastPanel specific headers
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Server $host;
        
        # Increase timeouts for FastPanel
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # Buffer settings for FastPanel
        proxy_buffering off;
        proxy_request_buffering off;
    }
    
    # Health check endpoint
    location /health {
        access_log off;
        return 200 "FastPanel Proxy OK\n";
        add_header Content-Type text/plain;
    }
}

# HTTPS Configuration (if SSL certificates are available)
server {
    listen 443 ssl http2;
    server_name fastpanel.streamdb.online;
    
    # SSL Configuration (update paths to your certificates)
    # ssl_certificate /etc/letsencrypt/live/streamdb.online/fullchain.pem;
    # ssl_certificate_key /etc/letsencrypt/live/streamdb.online/privkey.pem;
    
    # If no SSL certificates, redirect to HTTP for now
    return 301 http://$server_name$request_uri;
    
    # Uncomment below when SSL is configured and comment out the redirect above
    # 
    # # Security headers
    # add_header X-Frame-Options "SAMEORIGIN" always;
    # add_header X-Content-Type-Options "nosniff" always;
    # add_header X-XSS-Protection "1; mode=block" always;
    # add_header Referrer-Policy "no-referrer-when-downgrade" always;
    # 
    # # Proxy to backend FastPanel
    # location / {
    #     proxy_pass http://***********:5501;
    #     proxy_http_version 1.1;
    #     proxy_set_header Upgrade $http_upgrade;
    #     proxy_set_header Connection 'upgrade';
    #     proxy_set_header Host $host;
    #     proxy_set_header X-Real-IP $remote_addr;
    #     proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    #     proxy_set_header X-Forwarded-Proto $scheme;
    #     proxy_cache_bypass $http_upgrade;
    #     
    #     # FastPanel specific headers
    #     proxy_set_header X-Forwarded-Host $host;
    #     proxy_set_header X-Forwarded-Server $host;
    #     
    #     # Timeouts
    #     proxy_connect_timeout 60s;
    #     proxy_send_timeout 60s;
    #     proxy_read_timeout 60s;
    #     
    #     # Buffer settings
    #     proxy_buffering off;
    #     proxy_request_buffering off;
    # }
}
EOF
    
    echo ""
    log_info "🔧 Steps to implement on proxy server (*************):"
    echo ""
    echo "1. SSH to proxy server:"
    echo "   ssh root@*************"
    echo ""
    echo "2. Create the configuration file:"
    echo "   nano /etc/nginx/sites-available/fastpanel.streamdb.online"
    echo ""
    echo "3. Copy the configuration above into the file"
    echo ""
    echo "4. Enable the site:"
    echo "   ln -s /etc/nginx/sites-available/fastpanel.streamdb.online /etc/nginx/sites-enabled/"
    echo ""
    echo "5. Test Nginx configuration:"
    echo "   nginx -t"
    echo ""
    echo "6. Reload Nginx:"
    echo "   systemctl reload nginx"
    echo ""
    echo "7. Test access:"
    echo "   curl -I http://fastpanel.streamdb.online/health"
}

# Function to create proxy server setup script
create_proxy_setup_script() {
    log_info "📝 Creating Proxy Server Setup Script"
    echo "====================================="
    
    cat > proxy-server-setup.sh << 'EOF'
#!/bin/bash

# FastPanel Proxy Server Setup Script
# Run this script on your proxy server (*************)

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

# Check if running as root
if [[ $EUID -ne 0 ]]; then
    log_error "This script must be run as root (use sudo)"
    exit 1
fi

log_info "🔧 Setting up FastPanel proxy on proxy server"
echo "=============================================="

# Create FastPanel proxy configuration
log_info "Creating FastPanel proxy configuration..."

cat > /etc/nginx/sites-available/fastpanel.streamdb.online << 'NGINX_EOF'
# FastPanel Subdomain Proxy Configuration
server {
    listen 80;
    server_name fastpanel.streamdb.online;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    
    # Proxy to backend FastPanel
    location / {
        proxy_pass http://***********:5501;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # FastPanel specific headers
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Server $host;
        
        # Timeouts for FastPanel
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # Buffer settings
        proxy_buffering off;
        proxy_request_buffering off;
    }
    
    # Health check
    location /health {
        access_log off;
        return 200 "FastPanel Proxy OK\n";
        add_header Content-Type text/plain;
    }
}

# HTTPS redirect (update when SSL is configured)
server {
    listen 443 ssl http2;
    server_name fastpanel.streamdb.online;
    
    # Temporary redirect to HTTP
    return 301 http://$server_name$request_uri;
}
NGINX_EOF

# Enable the site
log_info "Enabling FastPanel proxy site..."
ln -sf /etc/nginx/sites-available/fastpanel.streamdb.online /etc/nginx/sites-enabled/

# Test Nginx configuration
log_info "Testing Nginx configuration..."
if nginx -t; then
    log_success "Nginx configuration is valid"
    
    # Reload Nginx
    log_info "Reloading Nginx..."
    systemctl reload nginx
    log_success "Nginx reloaded successfully"
else
    log_error "Nginx configuration test failed"
    exit 1
fi

# Test the configuration
log_info "Testing FastPanel proxy..."
sleep 2

if curl -s --connect-timeout 10 http://localhost/health -H "Host: fastpanel.streamdb.online" >/dev/null 2>&1; then
    log_success "FastPanel proxy is working locally"
else
    log_warning "FastPanel proxy may not be working (check backend server)"
fi

log_success "🎉 FastPanel proxy setup completed!"
log_info ""
log_info "🔍 Next steps:"
log_info "1. Test access: http://fastpanel.streamdb.online/"
log_info "2. Configure SSL certificates if needed"
log_info "3. Update DNS if fastpanel.streamdb.online doesn't point to this server"
EOF

    chmod +x proxy-server-setup.sh
    log_success "Proxy server setup script created: proxy-server-setup.sh"
    log_info "Copy this script to your proxy server (*************) and run it"
}

# Function to show DNS fix instructions
show_dns_fix_instructions() {
    log_info "🌐 DNS Configuration Fix"
    echo "========================"
    
    echo ""
    log_warning "IMPORTANT: DNS Issue Detected!"
    echo ""
    log_info "Current DNS configuration:"
    log_info "  fastpanel.streamdb.online → *********** (backend server) ❌"
    echo ""
    log_info "Required DNS configuration:"
    log_info "  fastpanel.streamdb.online → ************* (proxy server) ✅"
    echo ""
    log_info "🔧 To fix DNS:"
    log_info "1. Log into your DNS provider (Cloudflare, etc.)"
    log_info "2. Find the DNS record for 'fastpanel.streamdb.online'"
    log_info "3. Change the IP address from *********** to *************"
    log_info "4. Save the changes"
    log_info "5. Wait for DNS propagation (5-30 minutes)"
    echo ""
    log_info "🔍 Verify DNS change:"
    log_info "  nslookup fastpanel.streamdb.online"
    log_info "  (Should show *************)"
}

# Main execution
main() {
    log_info "🔍 Proxy Server Configuration Checker"
    log_info "====================================="
    
    check_dns_configuration
    echo ""
    test_proxy_server_connectivity
    echo ""
    generate_proxy_server_config
    echo ""
    create_proxy_setup_script
    echo ""
    show_dns_fix_instructions
    
    echo ""
    log_success "🎉 Proxy server configuration check completed!"
    log_info ""
    log_info "📋 Summary of required actions:"
    log_info "1. Fix DNS: Point fastpanel.streamdb.online to *************"
    log_info "2. Configure proxy server: Copy proxy-server-setup.sh to ************* and run it"
    log_info "3. Test access: https://fastpanel.streamdb.online/"
    log_info ""
    log_warning "⚠️  Both DNS and proxy configuration are required for FastPanel to work!"
}

# Run main function
main "$@"
