#!/bin/bash

# Update proxy server to use port 5502 for FastPanel

echo "Updating proxy server FastPanel configuration..."

# Update the Nginx configuration to use port 5502
sudo sed -i 's/proxy_pass http:\/\/45\.93\.8\.197:5501/proxy_pass http:\/\/45.93.8.197:5502/' /etc/nginx/sites-available/fastpanel.streamdb.online

# Test configuration
sudo nginx -t

if [ $? -eq 0 ]; then
    echo "Configuration valid, reloading Nginx..."
    sudo systemctl reload nginx
    
    echo "Testing FastPanel access..."
    sleep 2
    curl -I https://fastpanel.streamdb.online/
else
    echo "Configuration error!"
fi
