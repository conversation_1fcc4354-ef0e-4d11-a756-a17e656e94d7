#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');

console.log('🔧 StreamDB Production Fix Tool');
console.log('================================');

// Function to execute shell commands
function execCommand(command, description) {
  return new Promise((resolve, reject) => {
    console.log(`\n⚡ ${description}...`);
    console.log(`Command: ${command}`);
    
    exec(command, (error, stdout, stderr) => {
      if (error) {
        console.error(`❌ Error: ${error.message}`);
        reject(error);
        return;
      }
      
      if (stderr) {
        console.warn(`⚠️ Warning: ${stderr}`);
      }
      
      if (stdout) {
        console.log(`✅ Output: ${stdout}`);
      }
      
      console.log(`✅ ${description} completed`);
      resolve(stdout);
    });
  });
}

async function fixProduction() {
  try {
    console.log('\n🔍 Step 1: Checking current status...');
    
    // Check if we're in the right directory
    const currentDir = process.cwd();
    console.log('Current directory:', currentDir);
    
    // Check if dist exists
    const distPath = path.join(__dirname, '..', 'dist');
    const distExists = fs.existsSync(distPath);
    console.log('Dist directory exists:', distExists);
    
    if (!distExists) {
      console.log('\n🏗️ Step 2: Building application...');
      await execCommand('cd .. && npm run build', 'Building React application');
    } else {
      console.log('\n✅ Dist directory already exists');
    }
    
    console.log('\n🔄 Step 3: Stopping existing PM2 processes...');
    try {
      await execCommand('pm2 stop streamdb-online', 'Stopping streamdb-online process');
    } catch (error) {
      console.log('No existing process to stop (this is normal for first deployment)');
    }
    
    console.log('\n🧹 Step 4: Clearing PM2 logs...');
    try {
      await execCommand('pm2 flush', 'Clearing PM2 logs');
    } catch (error) {
      console.log('Could not clear PM2 logs (this is normal)');
    }
    
    console.log('\n🚀 Step 5: Starting server with production environment...');
    await execCommand('NODE_ENV=production pm2 start index.js --name streamdb-online --env production', 'Starting production server');
    
    console.log('\n💾 Step 6: Saving PM2 configuration...');
    await execCommand('pm2 save', 'Saving PM2 configuration');
    
    console.log('\n📊 Step 7: Checking server status...');
    await execCommand('pm2 status', 'Checking PM2 status');
    
    console.log('\n📋 Step 8: Showing recent logs...');
    await execCommand('pm2 logs streamdb-online --lines 20', 'Showing recent server logs');
    
    console.log('\n✅ Production fix completed successfully!');
    console.log('\n🌐 Your website should now be accessible at: https://streamdb.online');
    console.log('\n📝 Next steps:');
    console.log('1. Test the website in your browser');
    console.log('2. Check that favicon files load correctly');
    console.log('3. Verify that the React app loads without module errors');
    console.log('4. Test admin panel functionality');
    
  } catch (error) {
    console.error('\n❌ Production fix failed:', error.message);
    console.log('\n🔧 Manual troubleshooting steps:');
    console.log('1. Check if Node.js and PM2 are installed');
    console.log('2. Verify database connection');
    console.log('3. Check file permissions');
    console.log('4. Review server logs: pm2 logs streamdb-online');
    process.exit(1);
  }
}

// Run the fix
fixProduction();
