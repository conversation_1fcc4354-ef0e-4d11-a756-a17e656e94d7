#!/bin/bash

# StreamDB Online - Security Fix Verification Script
# This script verifies that the security fixes are working correctly

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROXY_SERVER_IP="*************"
BACKEND_SERVER_IP="***********"
DOMAIN="streamdb.online"

# Function to log messages
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to test direct IP access (should be blocked)
test_direct_access_blocked() {
    log_info "Testing direct IP access (should be BLOCKED)..."
    
    local failed_tests=0
    
    # Test direct HTTP access
    if curl --connect-timeout 10 --max-time 15 -s http://${BACKEND_SERVER_IP} >/dev/null 2>&1; then
        log_error "SECURITY ISSUE: Direct HTTP access to ${BACKEND_SERVER_IP} is NOT blocked!"
        failed_tests=$((failed_tests + 1))
    else
        log_success "Direct HTTP access to ${BACKEND_SERVER_IP} is properly blocked"
    fi
    
    # Test direct application port access
    if curl --connect-timeout 10 --max-time 15 -s http://${BACKEND_SERVER_IP}:3001 >/dev/null 2>&1; then
        log_error "SECURITY ISSUE: Direct access to ${BACKEND_SERVER_IP}:3001 is NOT blocked!"
        failed_tests=$((failed_tests + 1))
    else
        log_success "Direct access to ${BACKEND_SERVER_IP}:3001 is properly blocked"
    fi
    
    # Test old webhook port access
    if curl --connect-timeout 10 --max-time 15 -s http://${BACKEND_SERVER_IP}:9000 >/dev/null 2>&1; then
        log_error "SECURITY ISSUE: Direct access to ${BACKEND_SERVER_IP}:9000 is NOT blocked!"
        failed_tests=$((failed_tests + 1))
    else
        log_success "Direct access to ${BACKEND_SERVER_IP}:9000 is properly blocked"
    fi
    
    # Test MySQL port access
    if timeout 10 bash -c "</dev/tcp/${BACKEND_SERVER_IP}/3306" 2>/dev/null; then
        log_error "SECURITY ISSUE: Direct access to MySQL port 3306 is NOT blocked!"
        failed_tests=$((failed_tests + 1))
    else
        log_success "Direct access to MySQL port 3306 is properly blocked"
    fi
    
    return $failed_tests
}

# Function to test proxy access (should work)
test_proxy_access_working() {
    log_info "Testing proxy access (should WORK)..."
    
    local failed_tests=0
    
    # Test main website
    if curl --connect-timeout 15 --max-time 30 -s -I https://${DOMAIN} | grep -q "200\|301\|302"; then
        log_success "Website access through proxy: OK"
    else
        log_error "Website access through proxy: FAILED"
        failed_tests=$((failed_tests + 1))
    fi
    
    # Test API health endpoint
    if curl --connect-timeout 15 --max-time 30 -s https://${DOMAIN}/api/health | grep -q "OK\|status"; then
        log_success "API access through proxy: OK"
    else
        log_warning "API access through proxy: May not be responding (check if backend is running)"
        # Don't count as failure since backend might be down for maintenance
    fi
    
    # Test admin panel access
    if curl --connect-timeout 15 --max-time 30 -s -I https://${DOMAIN}/admin | grep -q "200\|301\|302"; then
        log_success "Admin panel access through proxy: OK"
    else
        log_warning "Admin panel access through proxy: May not be responding"
        # Don't count as failure since it might require authentication
    fi
    
    return $failed_tests
}

# Function to test local services (should work)
test_local_services() {
    log_info "Testing local services (should WORK)..."
    
    local failed_tests=0
    
    # Test local web server
    if curl --connect-timeout 5 --max-time 10 -s http://127.0.0.1 >/dev/null 2>&1; then
        log_success "Local web server: OK"
    else
        log_info "Local web server: Not responding on port 80 (may be normal)"
    fi
    
    # Test local application
    if curl --connect-timeout 5 --max-time 10 -s http://127.0.0.1:3001/api/health >/dev/null 2>&1; then
        log_success "Local application: OK"
    else
        log_warning "Local application: Not responding (check if PM2 processes are running)"
    fi
    
    # Test MySQL local connection
    if command -v mysql >/dev/null 2>&1; then
        if mysql -u root -e "SELECT 1;" >/dev/null 2>&1; then
            log_success "MySQL local connection: OK"
        else
            log_info "MySQL local connection: Requires authentication (normal)"
        fi
    else
        log_info "MySQL client not available for testing"
    fi
    
    return $failed_tests
}

# Function to check firewall status
check_firewall_status() {
    log_info "Checking firewall configuration..."
    
    if command -v ufw >/dev/null 2>&1; then
        echo ""
        log_info "UFW Status:"
        ufw status numbered
        echo ""
        
        # Check if UFW is active
        if ufw status | grep -q "Status: active"; then
            log_success "UFW firewall is active"
        else
            log_error "UFW firewall is NOT active!"
            return 1
        fi
        
        # Check for proxy server rules
        if ufw status | grep -q "${PROXY_SERVER_IP}"; then
            log_success "Proxy server rules found in UFW"
        else
            log_warning "Proxy server rules not found in UFW"
        fi
        
    else
        log_error "UFW is not installed!"
        return 1
    fi
    
    return 0
}

# Function to check running processes
check_running_processes() {
    log_info "Checking running processes..."
    
    echo ""
    log_info "PM2 Processes:"
    if command -v pm2 >/dev/null 2>&1; then
        pm2 list
    else
        log_warning "PM2 not found"
    fi
    
    echo ""
    log_info "Listening Ports:"
    netstat -tlnp | grep LISTEN | head -20
    
    echo ""
    log_info "Active Web Services:"
    systemctl list-units --type=service --state=active | grep -E "(apache|nginx|mysql|node)" || log_info "No matching services found"
}

# Function to generate security report
generate_security_report() {
    local direct_access_issues=$1
    local proxy_access_issues=$2
    local local_service_issues=$3
    local firewall_issues=$4
    
    echo ""
    log_info "🔍 SECURITY VERIFICATION REPORT"
    log_info "================================"
    
    if [ $direct_access_issues -eq 0 ]; then
        log_success "✅ Direct IP access properly blocked"
    else
        log_error "❌ $direct_access_issues direct access security issues found"
    fi
    
    if [ $proxy_access_issues -eq 0 ]; then
        log_success "✅ Proxy access working correctly"
    else
        log_error "❌ $proxy_access_issues proxy access issues found"
    fi
    
    if [ $firewall_issues -eq 0 ]; then
        log_success "✅ Firewall properly configured"
    else
        log_error "❌ Firewall configuration issues found"
    fi
    
    echo ""
    local total_issues=$((direct_access_issues + proxy_access_issues + firewall_issues))
    
    if [ $total_issues -eq 0 ]; then
        log_success "🎉 ALL SECURITY TESTS PASSED!"
        log_success "Your two-tier offshore proxy architecture is properly secured."
        echo ""
        log_info "✅ Backend server IP (${BACKEND_SERVER_IP}) is hidden from public internet"
        log_info "✅ All traffic flows through secure proxy chain"
        log_info "✅ Website functionality is preserved"
        log_info "✅ Attack surface significantly reduced"
    else
        log_error "🚨 $total_issues SECURITY ISSUES FOUND!"
        log_error "Please review the issues above and run the security fix script again."
        echo ""
        log_info "To fix issues, run: sudo bash secure-backend-server.sh"
    fi
    
    return $total_issues
}

# Main execution
main() {
    log_info "🔍 StreamDB Security Fix Verification"
    log_info "====================================="
    
    # Run tests
    test_direct_access_blocked
    direct_access_issues=$?
    
    echo ""
    test_proxy_access_working  
    proxy_access_issues=$?
    
    echo ""
    test_local_services
    local_service_issues=$?
    
    echo ""
    check_firewall_status
    firewall_issues=$?
    
    echo ""
    check_running_processes
    
    # Generate final report
    generate_security_report $direct_access_issues $proxy_access_issues $local_service_issues $firewall_issues
    
    return $?
}

# Run main function
main "$@"
