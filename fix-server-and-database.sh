#!/bin/bash

echo "🔧 StreamDB Server and Database Fix Script"
echo "=========================================="
echo "Single Server Setup: Production + Database on ***********"
echo ""

# Step 1: Kill existing Node.js processes
echo "1. Stopping existing Node.js processes..."
pkill -f "node index.js" || echo "No existing Node.js processes found"
pkill -f "npm start" || echo "No npm processes found"
sleep 3

# Step 2: Update environment variables for localhost database connection
echo "2. Updating database configuration for localhost..."
cd /var/www/streamdb_onl_usr/data/www/streamdb.online

# Update main .env file to use localhost for production
sed -i 's/DB_HOST=.*/DB_HOST=localhost/' .env
echo "✓ Updated main .env file to use localhost"

# Update server .env file to use localhost for production
sed -i 's/DB_HOST=.*/DB_HOST=localhost/' server/.env
echo "✓ Updated server .env file to use localhost"

# Step 3: Test database connection first
echo "3. Testing database connection..."
cd server
node test-db-connection.cjs
if [ $? -eq 0 ]; then
    echo "✅ Database connection successful"
else
    echo "❌ Database connection failed"
    echo "Please check if MySQL is configured to accept remote connections on ***********:3306"
    exit 1
fi

# Step 4: Start the server
echo "4. Starting Node.js server..."
npm start &
SERVER_PID=$!
echo "✓ Server started with PID: $SERVER_PID"

# Step 5: Wait a moment and check if server started successfully
sleep 5
if ps -p $SERVER_PID > /dev/null; then
    echo "✅ Server is running successfully"
    echo "🌐 Website should now be accessible at https://streamdb.online"
    echo "🔗 API should be accessible at https://streamdb.online/api"
else
    echo "❌ Server failed to start"
    echo "Check the logs above for errors"
    exit 1
fi

echo ""
echo "🎉 StreamDB Server Fix Complete!"
echo "==============================="
echo "Next steps:"
echo "1. Test your website at https://streamdb.online"
echo "2. Test admin panel login"
echo "3. Verify database connectivity from the website"
