#!/usr/bin/env node

/**
 * Environment File Synchronization Script
 * Ensures both .env files are synchronized while maintaining environment-specific differences
 */

const fs = require('fs');
const path = require('path');

class EnvSynchronizer {
  constructor() {
    this.rootEnvPath = '.env';
    this.serverEnvPath = 'server/.env';
    this.differences = [];
  }

  log(message, type = 'info') {
    const colors = {
      info: '\x1b[36m',
      success: '\x1b[32m',
      warning: '\x1b[33m',
      error: '\x1b[31m',
      reset: '\x1b[0m'
    };
    
    const icons = {
      info: 'ℹ️',
      success: '✅',
      warning: '⚠️',
      error: '❌'
    };
    
    console.log(`${colors[type]}${icons[type]} ${message}${colors.reset}`);
  }

  parseEnvFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const lines = content.split('\n');
      const env = {};
      const comments = {};
      
      lines.forEach((line, index) => {
        const trimmed = line.trim();
        
        // Store comments
        if (trimmed.startsWith('#') || trimmed === '') {
          comments[index] = line;
          return;
        }
        
        // Parse key=value pairs
        const match = line.match(/^([^=]+)=(.*)$/);
        if (match) {
          const key = match[1].trim();
          const value = match[2].trim();
          env[key] = value;
        }
      });
      
      return { env, comments, lines };
    } catch (error) {
      this.log(`Error reading ${filePath}: ${error.message}`, 'error');
      return null;
    }
  }

  compareEnvFiles() {
    this.log('Comparing .env files...', 'info');
    
    const rootEnv = this.parseEnvFile(this.rootEnvPath);
    const serverEnv = this.parseEnvFile(this.serverEnvPath);
    
    if (!rootEnv || !serverEnv) {
      this.log('Failed to parse one or both .env files', 'error');
      return false;
    }
    
    // Environment-specific differences that are expected
    const expectedDifferences = {
      'DB_HOST': {
        root: '***********',  // Development
        server: 'localhost'   // Production
      },
      'DB_DEV_HOST': {
        root: undefined,      // Not needed in root
        server: '***********' // Development fallback
      },
      'DB_PROD_HOST': {
        root: 'localhost',    // Production reference
        server: undefined     // Not needed in server
      },
      'DB_SSL': {
        root: 'true',         // Development needs SSL
        server: 'false'       // Production uses socket
      },
      'DB_REMOTE_ACCESS': {
        root: 'true',         // Development needs remote access
        server: 'false'       // Production is local only
      }
    };
    
    // Check for differences
    const allKeys = new Set([...Object.keys(rootEnv.env), ...Object.keys(serverEnv.env)]);
    
    for (const key of allKeys) {
      const rootValue = rootEnv.env[key];
      const serverValue = serverEnv.env[key];
      
      // Skip expected differences
      if (expectedDifferences[key]) {
        const expected = expectedDifferences[key];
        if (rootValue === expected.root && serverValue === expected.server) {
          continue; // This difference is expected
        }
      }
      
      // Check for unexpected differences
      if (rootValue !== serverValue) {
        this.differences.push({
          key,
          rootValue: rootValue || 'undefined',
          serverValue: serverValue || 'undefined',
          expected: !!expectedDifferences[key]
        });
      }
    }
    
    return true;
  }

  generateReport() {
    this.log('==========================================', 'info');
    this.log('🔍 Environment Files Synchronization Report', 'info');
    this.log('==========================================', 'info');
    
    if (this.differences.length === 0) {
      this.log('✅ All environment files are properly synchronized!', 'success');
      this.log('All differences are expected environment-specific configurations.', 'success');
      return;
    }
    
    this.log(`Found ${this.differences.length} differences:`, 'warning');
    
    const expectedDiffs = this.differences.filter(d => d.expected);
    const unexpectedDiffs = this.differences.filter(d => !d.expected);
    
    if (expectedDiffs.length > 0) {
      this.log('\n✅ Expected Environment-Specific Differences:', 'success');
      expectedDiffs.forEach(diff => {
        this.log(`   ${diff.key}:`, 'info');
        this.log(`     Root (.env): ${diff.rootValue}`, 'info');
        this.log(`     Server (server/.env): ${diff.serverValue}`, 'info');
      });
    }
    
    if (unexpectedDiffs.length > 0) {
      this.log('\n⚠️ Unexpected Differences (Need Attention):', 'warning');
      unexpectedDiffs.forEach(diff => {
        this.log(`   ${diff.key}:`, 'warning');
        this.log(`     Root (.env): ${diff.rootValue}`, 'warning');
        this.log(`     Server (server/.env): ${diff.serverValue}`, 'warning');
      });
      
      this.log('\n🔧 Recommended Actions:', 'info');
      this.log('1. Review unexpected differences above', 'info');
      this.log('2. Update the appropriate .env file(s)', 'info');
      this.log('3. Run this script again to verify synchronization', 'info');
    }
  }

  async syncFiles() {
    this.log('🔄 Starting Environment File Synchronization...', 'info');
    
    const success = this.compareEnvFiles();
    
    if (success) {
      this.generateReport();
      
      // Check if server .env exists on production server
      this.log('\n📋 Deployment Checklist:', 'info');
      this.log('✅ Root .env file: Present', 'success');
      this.log('✅ Server .env file: Present', 'success');
      this.log('✅ Environment-specific configurations: Correct', 'success');
      
      this.log('\n🚀 Next Steps:', 'info');
      this.log('1. Deploy server/.env to production server', 'info');
      this.log('2. Restart PM2 process to load new environment', 'info');
      this.log('3. Verify database connection on production', 'info');
    }
    
    return success;
  }

  // Utility method to update both files while preserving differences
  updateBothFiles(key, rootValue, serverValue = null) {
    this.log(`Updating ${key} in both .env files...`, 'info');
    
    try {
      // Update root .env
      this.updateEnvFile(this.rootEnvPath, key, rootValue);
      
      // Update server .env (use serverValue if provided, otherwise same as root)
      this.updateEnvFile(this.serverEnvPath, key, serverValue || rootValue);
      
      this.log(`Successfully updated ${key} in both files`, 'success');
      return true;
    } catch (error) {
      this.log(`Error updating ${key}: ${error.message}`, 'error');
      return false;
    }
  }

  updateEnvFile(filePath, key, value) {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    
    let updated = false;
    const newLines = lines.map(line => {
      if (line.startsWith(`${key}=`)) {
        updated = true;
        return `${key}=${value}`;
      }
      return line;
    });
    
    // If key wasn't found, add it
    if (!updated) {
      newLines.push(`${key}=${value}`);
    }
    
    fs.writeFileSync(filePath, newLines.join('\n'));
  }
}

// Command line interface
if (require.main === module) {
  const synchronizer = new EnvSynchronizer();
  
  const command = process.argv[2];
  
  switch (command) {
    case 'check':
    case undefined:
      synchronizer.syncFiles();
      break;
    case 'update':
      const key = process.argv[3];
      const rootValue = process.argv[4];
      const serverValue = process.argv[5];
      
      if (!key || !rootValue) {
        console.log('Usage: node sync-env-files.cjs update KEY ROOT_VALUE [SERVER_VALUE]');
        process.exit(1);
      }
      
      synchronizer.updateBothFiles(key, rootValue, serverValue);
      break;
    default:
      console.log('Usage:');
      console.log('  node sync-env-files.cjs check    - Check synchronization');
      console.log('  node sync-env-files.cjs update KEY VALUE [SERVER_VALUE] - Update both files');
      break;
  }
}

module.exports = EnvSynchronizer;
