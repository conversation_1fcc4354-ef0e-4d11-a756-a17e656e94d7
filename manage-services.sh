#!/bin/bash

# StreamDB Service Management Script
# Manages all services for streamdb.online

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if PM2 is installed
check_pm2() {
    if ! command -v pm2 &> /dev/null; then
        print_error "PM2 is not installed. Installing PM2..."
        npm install -g pm2
        if [ $? -ne 0 ]; then
            print_error "Failed to install PM2"
            exit 1
        fi
    fi
}

# Function to start all services
start_services() {
    print_status "Starting all StreamDB services..."
    
    check_pm2
    
    # Create logs directory if it doesn't exist
    mkdir -p logs
    
    # Start all services using PM2
    pm2 start ecosystem.config.js
    
    if [ $? -eq 0 ]; then
        print_status "All services started successfully"
        pm2 status
    else
        print_error "Failed to start services"
        exit 1
    fi
}

# Function to stop all services
stop_services() {
    print_status "Stopping all StreamDB services..."
    
    pm2 stop ecosystem.config.js
    
    if [ $? -eq 0 ]; then
        print_status "All services stopped successfully"
    else
        print_warning "Some services may not have stopped cleanly"
    fi
}

# Function to restart all services
restart_services() {
    print_status "Restarting all StreamDB services..."
    
    pm2 restart ecosystem.config.js
    
    if [ $? -eq 0 ]; then
        print_status "All services restarted successfully"
        pm2 status
    else
        print_error "Failed to restart services"
        exit 1
    fi
}

# Function to check service status
status_services() {
    print_status "Checking service status..."
    
    if command -v pm2 &> /dev/null; then
        pm2 status
        echo ""
        pm2 monit
    else
        print_warning "PM2 not installed, cannot check service status"
    fi
}

# Function to check service health
health_check() {
    print_status "Performing health checks..."
    
    # Check main application
    echo "Checking main application (port 3001)..."
    if curl -s http://localhost:3001/api/health > /dev/null; then
        print_status "Main application: HEALTHY"
    else
        print_error "Main application: UNHEALTHY"
    fi
    
    # Check MySQL health service
    echo "Checking MySQL health service (port 3307)..."
    if curl -s http://localhost:3307/health > /dev/null; then
        print_status "MySQL health service: HEALTHY"
    else
        print_error "MySQL health service: UNHEALTHY"
    fi
    
    # Check webhook service
    echo "Checking webhook service (port 9000)..."
    if curl -s http://localhost:9000/health > /dev/null; then
        print_status "Webhook service: HEALTHY"
    else
        print_error "Webhook service: UNHEALTHY"
    fi
    
    # Check Nginx (if running)
    echo "Checking Nginx (port 8080)..."
    if curl -s http://localhost:8080/health > /dev/null; then
        print_status "Nginx health endpoint: HEALTHY"
    else
        print_warning "Nginx health endpoint: NOT RESPONDING"
    fi
}

# Function to view logs
view_logs() {
    local service=$1
    
    if [ -z "$service" ]; then
        print_status "Available services: streamdb-main, mysql-health, github-webhook"
        read -p "Enter service name to view logs: " service
    fi
    
    case $service in
        "main"|"streamdb-main")
            pm2 logs streamdb-main
            ;;
        "mysql"|"mysql-health")
            pm2 logs mysql-health
            ;;
        "webhook"|"github-webhook")
            pm2 logs github-webhook
            ;;
        "all")
            pm2 logs
            ;;
        *)
            print_error "Unknown service: $service"
            print_status "Available services: streamdb-main, mysql-health, github-webhook, all"
            ;;
    esac
}

# Function to deploy latest changes
deploy() {
    print_status "Deploying latest changes..."
    
    # Stop services
    stop_services
    
    # Pull latest changes (if in git repo)
    if [ -d ".git" ]; then
        print_status "Pulling latest changes from git..."
        git pull origin main
    fi
    
    # Install dependencies
    if [ -f "package.json" ]; then
        print_status "Installing dependencies..."
        npm install
    fi
    
    # Build if needed
    if [ -f "package.json" ] && grep -q "build" package.json; then
        print_status "Building application..."
        npm run build
    fi
    
    # Start services
    start_services
    
    # Health check
    sleep 5
    health_check
}

# Function to setup firewall rules
setup_firewall() {
    print_status "Setting up firewall rules..."
    
    if command -v ufw &> /dev/null; then
        # Ubuntu/Debian with UFW
        sudo ufw allow 22/tcp     # SSH
        sudo ufw allow 80/tcp     # HTTP
        sudo ufw allow 443/tcp    # HTTPS
        sudo ufw allow 3001/tcp   # Main app (internal)
        sudo ufw allow 8080/tcp   # Nginx health
        sudo ufw allow 9000/tcp   # GitHub webhooks
        sudo ufw allow 3307/tcp   # MySQL health
        sudo ufw --force enable
        print_status "UFW firewall rules applied"
    elif command -v firewall-cmd &> /dev/null; then
        # CentOS/RHEL with firewalld
        sudo firewall-cmd --permanent --add-port=22/tcp
        sudo firewall-cmd --permanent --add-port=80/tcp
        sudo firewall-cmd --permanent --add-port=443/tcp
        sudo firewall-cmd --permanent --add-port=3001/tcp
        sudo firewall-cmd --permanent --add-port=8080/tcp
        sudo firewall-cmd --permanent --add-port=9000/tcp
        sudo firewall-cmd --permanent --add-port=3307/tcp
        sudo firewall-cmd --reload
        print_status "Firewalld rules applied"
    else
        print_warning "No supported firewall detected (ufw or firewalld)"
    fi
}

# Function to show help
show_help() {
    echo "StreamDB Service Management Script"
    echo ""
    echo "Usage: $0 {start|stop|restart|status|health|logs|deploy|firewall|help}"
    echo ""
    echo "Commands:"
    echo "  start    - Start all services"
    echo "  stop     - Stop all services"
    echo "  restart  - Restart all services"
    echo "  status   - Show service status"
    echo "  health   - Perform health checks"
    echo "  logs     - View service logs"
    echo "  deploy   - Deploy latest changes"
    echo "  firewall - Setup firewall rules"
    echo "  help     - Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start"
    echo "  $0 logs main"
    echo "  $0 health"
}

# Main script logic
case "${1:-}" in
    "start")
        start_services
        ;;
    "stop")
        stop_services
        ;;
    "restart")
        restart_services
        ;;
    "status")
        status_services
        ;;
    "health")
        health_check
        ;;
    "logs")
        view_logs "$2"
        ;;
    "deploy")
        deploy
        ;;
    "firewall")
        setup_firewall
        ;;
    "help"|"--help"|"-h")
        show_help
        ;;
    "")
        print_error "No command specified"
        echo ""
        show_help
        exit 1
        ;;
    *)
        print_error "Unknown command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
