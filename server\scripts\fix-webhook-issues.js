#!/usr/bin/env node

/**
 * StreamDB Webhook Issues Fix Script
 * Comprehensive fix for GitHub webhook 500 errors and deployment issues
 */

const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');
const execAsync = promisify(exec);

require('dotenv').config({ path: path.join(__dirname, '../.env') });

class WebhookFixer {
  constructor() {
    this.fixes = [];
    this.errors = [];
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const colors = {
      info: '\x1b[34m',
      success: '\x1b[32m',
      warning: '\x1b[33m',
      error: '\x1b[31m'
    };
    
    console.log(`${colors[type]}[${timestamp}] ${message}\x1b[0m`);
    
    if (type === 'success') {
      this.fixes.push(message);
    } else if (type === 'error') {
      this.errors.push(message);
    }
  }

  async fixDeploymentScript() {
    this.log('🔧 Fixing deployment script issues...', 'info');
    
    const deployScript = process.env.DEPLOY_SCRIPT;
    
    if (!deployScript) {
      this.log('DEPLOY_SCRIPT environment variable not set', 'error');
      return false;
    }

    if (!fs.existsSync(deployScript)) {
      this.log(`Deploy script not found: ${deployScript}`, 'error');
      return false;
    }

    try {
      // Make script executable
      fs.chmodSync(deployScript, '755');
      this.log('Deploy script made executable', 'success');

      // Test script syntax
      await execAsync(`bash -n "${deployScript}"`);
      this.log('Deploy script syntax validated', 'success');

      return true;
    } catch (error) {
      this.log(`Deploy script fix failed: ${error.message}`, 'error');
      return false;
    }
  }

  async initializeDatabase() {
    this.log('🗄️ Initializing deployment database...', 'info');

    try {
      const initScript = path.join(__dirname, 'simple-db-init.js');

      if (!fs.existsSync(initScript)) {
        this.log('Database initialization script not found', 'error');
        return false;
      }

      await execAsync(`node "${initScript}"`, { timeout: 60000 });
      this.log('Database tables initialized successfully', 'success');
      return true;
    } catch (error) {
      this.log(`Database initialization failed: ${error.message}`, 'error');
      return false;
    }
  }

  async fixPM2Configuration() {
    this.log('🔄 Fixing PM2 configuration...', 'info');
    
    try {
      // Check if main app is running
      const { stdout } = await execAsync('pm2 jlist');
      const processes = JSON.parse(stdout);
      
      const mainApp = processes.find(p => 
        p.name === 'streamdb-online' || 
        p.name === 'index' || 
        p.name === 'streamdb_onl_usr'
      );

      if (!mainApp) {
        this.log('Starting main application with PM2...', 'info');
        const serverPath = path.join(__dirname, '../index.js');
        await execAsync(`pm2 start "${serverPath}" --name "streamdb-online" --env production`);
        this.log('Main application started with PM2', 'success');
      } else {
        this.log('Main application already running, restarting...', 'info');
        await execAsync(`pm2 restart ${mainApp.name}`);
        this.log('Main application restarted', 'success');
      }

      // Save PM2 configuration
      await execAsync('pm2 save');
      this.log('PM2 configuration saved', 'success');

      return true;
    } catch (error) {
      this.log(`PM2 configuration fix failed: ${error.message}`, 'error');
      return false;
    }
  }

  async testWebhookEndpoint() {
    this.log('🧪 Testing webhook endpoint...', 'info');

    try {
      const testUrl = `http://localhost:${process.env.PORT || 3001}/api/webhook/health`;

      // Use curl instead of fetch for better compatibility
      const { stdout } = await execAsync(`curl -s -o /dev/null -w "%{http_code}" "${testUrl}"`);

      if (stdout.trim() === '200') {
        this.log('Webhook health endpoint responding correctly', 'success');
        return true;
      } else {
        this.log(`Webhook health endpoint returned status: ${stdout.trim()}`, 'warning');
        return false;
      }
    } catch (error) {
      this.log(`Webhook endpoint test failed: ${error.message}`, 'error');
      return false;
    }
  }

  async fixNginxConfiguration() {
    this.log('🌐 Checking Nginx configuration...', 'info');
    
    try {
      // Test nginx configuration
      await execAsync('nginx -t');
      this.log('Nginx configuration is valid', 'success');

      // Reload nginx
      await execAsync('systemctl reload nginx');
      this.log('Nginx reloaded successfully', 'success');

      return true;
    } catch (error) {
      this.log(`Nginx configuration check failed: ${error.message}`, 'warning');
      // Don't fail the entire process for nginx issues
      return true;
    }
  }

  async createLogDirectories() {
    this.log('📁 Creating log directories...', 'info');
    
    try {
      const logDirs = [
        path.join(__dirname, '../logs'),
        '/var/log/streamdb',
        '/var/backups/streamdb-online'
      ];

      for (const dir of logDirs) {
        if (!fs.existsSync(dir)) {
          fs.mkdirSync(dir, { recursive: true, mode: 0o755 });
          this.log(`Created directory: ${dir}`, 'success');
        }
      }

      return true;
    } catch (error) {
      this.log(`Log directory creation failed: ${error.message}`, 'error');
      return false;
    }
  }

  async validateEnvironmentVariables() {
    this.log('🔍 Validating environment variables...', 'info');
    
    const requiredVars = [
      'WEBHOOK_SECRET',
      'DB_HOST',
      'DB_USER',
      'DB_PASSWORD',
      'DB_NAME',
      'DEPLOY_SCRIPT',
      'GITHUB_REPO'
    ];

    let allValid = true;

    for (const varName of requiredVars) {
      if (!process.env[varName]) {
        this.log(`Missing environment variable: ${varName}`, 'error');
        allValid = false;
      } else {
        this.log(`Environment variable ${varName} is set`, 'success');
      }
    }

    // Validate webhook secret strength
    const webhookSecret = process.env.WEBHOOK_SECRET;
    if (webhookSecret && webhookSecret.length < 32) {
      this.log('WEBHOOK_SECRET should be at least 32 characters long', 'warning');
    }

    return allValid;
  }

  async runComprehensiveFix() {
    this.log('🚀 Starting comprehensive webhook fix...', 'info');
    this.log('=' .repeat(60), 'info');

    const fixes = [
      { name: 'Environment Variables', fn: () => this.validateEnvironmentVariables() },
      { name: 'Log Directories', fn: () => this.createLogDirectories() },
      { name: 'Deployment Script', fn: () => this.fixDeploymentScript() },
      { name: 'Database Initialization', fn: () => this.initializeDatabase() },
      { name: 'PM2 Configuration', fn: () => this.fixPM2Configuration() },
      { name: 'Webhook Endpoint', fn: () => this.testWebhookEndpoint() },
      { name: 'Nginx Configuration', fn: () => this.fixNginxConfiguration() }
    ];

    const results = {};

    for (const fix of fixes) {
      this.log(`\n🔧 Running fix: ${fix.name}`, 'info');
      try {
        results[fix.name] = await fix.fn();
        if (results[fix.name]) {
          this.log(`✅ ${fix.name} - COMPLETED`, 'success');
        } else {
          this.log(`❌ ${fix.name} - FAILED`, 'error');
        }
      } catch (error) {
        this.log(`❌ ${fix.name} - ERROR: ${error.message}`, 'error');
        results[fix.name] = false;
      }
    }

    // Summary
    this.log('\n📊 Fix Summary:', 'info');
    this.log('=' .repeat(60), 'info');

    const successful = Object.values(results).filter(r => r).length;
    const total = Object.keys(results).length;

    for (const [name, success] of Object.entries(results)) {
      this.log(`${success ? '✅' : '❌'} ${name}`, success ? 'success' : 'error');
    }

    this.log(`\n🎯 Overall: ${successful}/${total} fixes completed successfully`, 'info');

    if (this.fixes.length > 0) {
      this.log('\n✅ Successful fixes:', 'success');
      this.fixes.forEach(fix => this.log(`  • ${fix}`, 'success'));
    }

    if (this.errors.length > 0) {
      this.log('\n❌ Issues that need attention:', 'error');
      this.errors.forEach(error => this.log(`  • ${error}`, 'error'));
    }

    // Next steps
    this.log('\n🔄 Recommended next steps:', 'info');
    this.log('1. Test webhook endpoint manually:', 'info');
    this.log(`   curl -X GET https://streamdb.online/api/webhook/test`, 'info');
    this.log('2. Check PM2 status:', 'info');
    this.log(`   pm2 status`, 'info');
    this.log('3. Monitor logs:', 'info');
    this.log(`   pm2 logs streamdb-online --lines 50`, 'info');
    this.log('4. Test GitHub webhook delivery in repository settings', 'info');

    return successful === total;
  }
}

// Run fixes if called directly
if (require.main === module) {
  const fixer = new WebhookFixer();
  fixer.runComprehensiveFix()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Fix script failed:', error);
      process.exit(1);
    });
}

module.exports = WebhookFixer;
