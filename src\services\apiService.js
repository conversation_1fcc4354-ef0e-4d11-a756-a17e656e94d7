/**
 * API Service for Database Integration
 * Handles all communication with the backend API
 * NO CREDENTIALS EXPOSED - All security handled server-side
 */

// API Configuration - NO sensitive data here
const API_BASE_URL = process.env.NODE_ENV === 'production'
  ? '/api'  // Production: same domain
  : 'http://localhost:3001/api';  // Development: local server

// Check if we're in a preview/build environment without backend
const isPreviewMode = window.location.port === '4173' || window.location.hostname === 'localhost';

// Session management using secure HTTP-only cookies and database storage
// No client-side token storage for enhanced security

// API Client Class - Now uses secure HTTP-only cookies for authentication
class ApiService {
  constructor() {
    this.baseURL = API_BASE_URL;
    // No client-side token storage - authentication handled via secure HTTP-only cookies
  }

  // Session management (secure) - No client-side token storage
  async checkAuthStatus() {
    try {
      const response = await this.request('/auth/status');
      return response && response.authenticated;
    } catch (error) {
      return false;
    }
  }

  async refreshSession() {
    try {
      const response = await this.request('/auth/refresh', { method: 'POST' });
      return response && response.success;
    } catch (error) {
      return false;
    }
  }

  // HTTP request helper with cookie-based authentication
  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;

    // If in preview mode without backend, return mock data
    if (isPreviewMode && !window.location.search.includes('use-api=true')) {
      return this.getMockResponse(endpoint, options.method || 'GET');
    }

    const config = {
      credentials: 'include', // Include HTTP-only cookies for authentication
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    };

    // No client-side token management - authentication handled via secure cookies

    try {
      const response = await fetch(url, config);

      // Handle authentication errors
      if (response.status === 401) {
        console.warn('Authentication required - session may have expired');
        // Optionally redirect to login page
        // window.location.href = '/admin/login';
        throw new Error('Authentication required');
      }

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Request failed');
      }

      return data;
    } catch (error) {
      // In preview mode, fallback to mock data
      if (isPreviewMode) {
        console.warn('API failed, falling back to mock data');
        return this.getMockResponse(endpoint, options.method || 'GET');
      }
      throw error;
    }
  }

  // Mock response for preview mode
  getMockResponse(endpoint, method) {
    console.log(`Mock API response for ${method} ${endpoint}`);

    if (endpoint.includes('/content')) {
      return {
        success: true,
        data: [
          {
            id: "mock-1",
            title: "Sample Movie",
            description: "This is a sample movie for preview",
            year: 2024,
            genres: ["Action", "Adventure"],
            type: "movie",
            image: "/placeholder-image.jpg",
            coverImage: "/placeholder-image.jpg",
            createdAt: new Date().toISOString(),
            isPublished: true,
            isFeatured: true,
            addToCarousel: true
          },
          {
            id: "mock-2",
            title: "Sample Series",
            description: "This is a sample series for preview",
            year: 2024,
            genres: ["Drama", "Thriller"],
            type: "series",
            image: "/placeholder-image.jpg",
            coverImage: "/placeholder-image.jpg",
            createdAt: new Date().toISOString(),
            isPublished: true,
            isFeatured: false,
            addToCarousel: true
          }
        ]
      };
    }

    if (endpoint.includes('/auth/status')) {
      return { authenticated: false };
    }

    if (endpoint.includes('/tracking') || endpoint.includes('/security-logs')) {
      return { success: true, message: 'Mock tracking logged' };
    }

    return { success: true, data: [] };
  }

  // Authentication methods - Now using secure HTTP-only cookies
  async login(credentials) {
    try {
      // Handle both object and individual parameters for backward compatibility
      const loginData = typeof credentials === 'object' && credentials.username 
        ? { username: credentials.username, password: credentials.password }
        : { username: credentials, password: arguments[1] };

      const response = await this.request('/auth/login', {
        method: 'POST',
        body: JSON.stringify(loginData)
      });

      // No client-side token storage - authentication handled via secure HTTP-only cookies
      return { success: true, ...response };
    } catch (error) {
      console.error('Login API error:', error);
      return { 
        success: false, 
        error: error.message || 'Authentication failed',
        details: error
      };
    }
  }

  async logout() {
    try {
      await this.request('/auth/logout', { method: 'POST' });
    } catch (error) {
      // Silent fail - user is likely already logged out
    }
    // No client-side token cleanup needed - server handles cookie invalidation
  }

  async verifySession() {
    try {
      const response = await this.request('/auth/verify');
      return response.user;
    } catch (error) {
      return null;
    }
  }

  async changePassword(currentPassword, newPassword) {
    return await this.request('/auth/change-password', {
      method: 'POST',
      body: JSON.stringify({ currentPassword, newPassword })
    });
  }

  // Content management methods
  async getContent(filters = {}) {
    const params = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, value);
      }
    });

    const queryString = params.toString();
    const endpoint = `/content${queryString ? `?${queryString}` : ''}`;
    
    return await this.request(endpoint);
  }

  async getContentById(id) {
    return await this.request(`/content/${id}`);
  }

  async createContent(contentData) {
    return await this.request('/content', {
      method: 'POST',
      body: JSON.stringify(contentData)
    });
  }

  async updateContent(id, contentData) {
    return await this.request(`/content/${id}`, {
      method: 'PUT',
      body: JSON.stringify(contentData)
    });
  }

  async deleteContent(id) {
    return await this.request(`/content/${id}`, {
      method: 'DELETE'
    });
  }

  async bulkContentOperation(operation, contentIds, data = {}) {
    return await this.request('/content/bulk', {
      method: 'POST',
      body: JSON.stringify({ operation, contentIds, data })
    });
  }

  // Category methods
  async getCategories(activeOnly = true, type = null) {
    const params = new URLSearchParams();
    if (activeOnly) params.append('active_only', 'true');
    if (type) params.append('type', type);
    
    const queryString = params.toString();
    const endpoint = `/categories${queryString ? `?${queryString}` : ''}`;
    
    return await this.request(endpoint);
  }

  async getCategoryById(id) {
    return await this.request(`/categories/${id}`);
  }

  async createCategory(categoryData) {
    return await this.request('/categories', {
      method: 'POST',
      body: JSON.stringify(categoryData)
    });
  }

  async updateCategory(id, categoryData) {
    return await this.request(`/categories/${id}`, {
      method: 'PUT',
      body: JSON.stringify(categoryData)
    });
  }

  async deleteCategory(id) {
    return await this.request(`/categories/${id}`, {
      method: 'DELETE'
    });
  }

  async getContentByCategory(slug, page = 1, limit = 20) {
    return await this.request(`/categories/${slug}/content?page=${page}&limit=${limit}`);
  }

  // File upload methods
  async uploadImage(file, optimize = true) {
    const formData = new FormData();
    formData.append('image', file);

    return await this.request(`/upload/image?optimize=${optimize}`, {
      method: 'POST',
      headers: {
        // Remove Content-Type to let browser set it with boundary
        // Authentication handled via HTTP-only cookies
      },
      body: formData
    });
  }

  async uploadImages(files, optimize = true) {
    const formData = new FormData();
    files.forEach(file => formData.append('images', file));

    return await this.request(`/upload/images?optimize=${optimize}`, {
      method: 'POST',
      headers: {
        // Authentication handled via HTTP-only cookies
      },
      body: formData
    });
  }

  async uploadSubtitle(file) {
    const formData = new FormData();
    formData.append('subtitle', file);

    return await this.request('/upload/subtitle', {
      method: 'POST',
      headers: {
        // Authentication handled via HTTP-only cookies
      },
      body: formData
    });
  }

  async deleteFile(type, filename) {
    return await this.request(`/upload/${type}/${filename}`, {
      method: 'DELETE'
    });
  }

  // Admin methods
  async getDashboardStats() {
    return await this.request('/admin/dashboard');
  }

  async getUsers() {
    return await this.request('/admin/users');
  }

  async updateUserStatus(userId, isActive) {
    return await this.request(`/admin/users/${userId}/status`, {
      method: 'PATCH',
      body: JSON.stringify({ is_active: isActive })
    });
  }

  async getSecurityLogs(page = 1, limit = 50, filters = {}) {
    const params = new URLSearchParams({ page, limit });
    Object.entries(filters).forEach(([key, value]) => {
      if (value) params.append(key, value);
    });

    return await this.request(`/admin/security-logs?${params.toString()}`);
  }

  async exportContent(type = null, publishedOnly = false) {
    const params = new URLSearchParams();
    if (type) params.append('type', type);
    if (publishedOnly) params.append('published_only', 'true');

    const response = await fetch(`${this.baseURL}/admin/export/content?${params.toString()}`, {
      credentials: 'include', // Include HTTP-only cookies for authentication
      headers: {
        // Authentication handled via HTTP-only cookies
      }
    });

    if (!response.ok) {
      throw new Error('Export failed');
    }

    // Handle file download
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `content_export_${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  }

  async getSystemHealth() {
    return await this.request('/admin/health');
  }

  // Secure embed link methods (NO LINKS EXPOSED)
  async getSecureEmbedLink(contentId) {
    return await this.request(`/embed/${contentId}`);
  }

  // Utility methods
  async testConnection() {
    try {
      const response = await fetch(`${this.baseURL}/health`);
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  // Search methods
  async searchContent(query, filters = {}) {
    return await this.getContent({
      search: query,
      ...filters
    });
  }

  // Homepage content methods
  async getHomepageContent() {
    const [movies, series, featured] = await Promise.all([
      this.getContent({ type: 'movie', published: true, limit: 20 }),
      this.getContent({ type: 'series', published: true, limit: 20 }),
      this.getContent({ featured: true, published: true, limit: 10 })
    ]);

    return {
      movies: movies.data || [],
      series: series.data || [],
      featured: featured.data || []
    };
  }

  // Category-specific content
  async getMoviesByCategory(category, page = 1) {
    return await this.getContent({
      type: 'movie',
      category,
      published: true,
      page,
      limit: 20
    });
  }

  async getSeriesByCategory(category, page = 1) {
    return await this.getContent({
      type: 'series',
      category,
      published: true,
      page,
      limit: 20
    });
  }
}

// Create and export singleton instance
const apiService = new ApiService();
export default apiService;

// Export individual methods for convenience
export const {
  login,
  logout,
  verifyToken,
  getContent,
  getContentById,
  createContent,
  updateContent,
  deleteContent,
  getCategories,
  uploadImage,
  getDashboardStats,
  getHomepageContent,
  searchContent
} = apiService;
