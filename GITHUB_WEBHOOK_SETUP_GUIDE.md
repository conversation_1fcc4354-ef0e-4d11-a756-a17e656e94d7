# 🚀 GitHub Webhook Auto-Deployment Setup Guide

## 🎯 Overview

This guide will help you configure GitHub webhooks to automatically deploy your StreamDB website whenever you push code to the main branch. The webhook now runs on port 443 (HTTPS) instead of port 9000, making it accessible through your existing web server.

## ✅ Prerequisites

Before setting up the webhook, ensure:
- [x] Your StreamDB server is running on `https://streamdb.online`
- [x] The webhook endpoint is accessible at `https://streamdb.online/api/webhook/github`
- [x] You have admin access to your GitHub repository
- [x] The deployment database schema is initialized
- [x] Your `.env` file contains a `WEBHOOK_SECRET`

## 🔧 Step 1: Initialize Deployment Database

First, set up the database tables for deployment tracking:

```bash
# On your server, run:
cd /var/www/streamdb_onl_usr/data/www/streamdb.online/server
node scripts/init-deployment-db.js
```

This creates the necessary tables for:
- Deployment tracking
- Webhook event logging
- Security event monitoring
- Admin panel integration

## 🔐 Step 2: Configure Webhook Secret

1. **Generate a secure webhook secret** (if not already done):
   ```bash
   # Generate a random 64-character secret
   openssl rand -hex 32
   ```

2. **Add to your server's `.env` file**:
   ```bash
   # In /var/www/streamdb_onl_usr/data/www/streamdb.online/server/.env
   WEBHOOK_SECRET=your_generated_secret_here
   GITHUB_REPO=aakash171088/Streaming_DB
   ```

3. **Restart your server**:
   ```bash
   pm2 restart streamdb-online
   ```

## 🌐 Step 3: Configure GitHub Webhook

### 3.1 Access Repository Settings

1. Go to your GitHub repository: `https://github.com/aakash171088/Streaming_DB`
2. Click **Settings** (in the repository menu)
3. In the left sidebar, click **Webhooks**
4. Click **Add webhook**

### 3.2 Configure Webhook Settings

Fill in the webhook configuration:

| Setting | Value |
|---------|-------|
| **Payload URL** | `https://streamdb.online/api/webhook/github` |
| **Content type** | `application/json` |
| **Secret** | Your `WEBHOOK_SECRET` from the `.env` file |
| **SSL verification** | ✅ Enable SSL verification |
| **Which events would you like to trigger this webhook?** | Just the push event |
| **Active** | ✅ Checked |

### 3.3 Save and Test

1. Click **Add webhook**
2. GitHub will immediately send a test payload
3. Check the **Recent Deliveries** tab to verify the webhook is working
4. Look for a green checkmark ✅ indicating successful delivery

## 🧪 Step 4: Test the Webhook

### 4.1 Test from Admin Panel

1. Go to your admin panel: `https://streamdb.online/admin`
2. Click the **GitHub Deployment** tab
3. Click **Test Webhook Connection**
4. Verify all checks pass:
   - ✅ Webhook URL accessible
   - ✅ Secret configured
   - ✅ Deploy script exists
   - ✅ Database connected

### 4.2 Test with a Real Push

1. Make a small change to your repository (e.g., update README.md)
2. Commit and push to the `main` branch:
   ```bash
   git add .
   git commit -m "Test webhook deployment"
   git push origin main
   ```
3. Check the admin panel **Deployments** tab to see the deployment progress
4. Monitor the **Logs** tab for detailed deployment information

## 📊 Step 5: Monitor Deployments

### 5.1 Admin Panel Monitoring

The admin panel provides comprehensive monitoring:

- **Status Tab**: Overview of webhook configuration and statistics
- **Deployments Tab**: Recent deployment attempts and their status
- **Logs Tab**: Detailed deployment logs and error messages
- **Configuration Tab**: GitHub webhook setup instructions

### 5.2 Available Actions

From the admin panel, you can:
- **Test Webhook**: Verify webhook connectivity and configuration
- **Check Status**: View recent deployments and statistics
- **Manual Deploy**: Trigger a deployment manually
- **View Logs**: Access detailed deployment logs

## 🔒 Security Features

The webhook implementation includes comprehensive security:

### Rate Limiting
- **Webhook requests**: 50 requests per 15 minutes per IP
- **Deployments**: 10 deployments per hour maximum
- **Admin requests**: Separate rate limiting for admin panel

### Signature Verification
- All webhook payloads are verified using HMAC-SHA256
- Invalid signatures are rejected and logged
- Timing-safe comparison prevents timing attacks

### IP Validation
- Webhook requests are validated against expected sources
- Security events are logged for monitoring
- Admin panel requests bypass IP restrictions (use authentication)

### Payload Validation
- JSON structure validation
- Repository verification (only your repo can trigger deployments)
- Branch filtering (only `main` branch triggers deployments)
- Payload size limits (1MB maximum)

## 🚨 Troubleshooting

### Common Issues

#### 1. Webhook Returns 401 Unauthorized
**Cause**: Invalid or missing webhook secret
**Solution**: 
- Verify `WEBHOOK_SECRET` in your `.env` file
- Ensure the secret in GitHub matches exactly
- Restart your server after changing the secret

#### 2. Webhook Returns 404 Not Found
**Cause**: Webhook URL is incorrect
**Solution**: 
- Verify the URL is `https://streamdb.online/api/webhook/github`
- Check that your server is running
- Test the URL manually: `curl https://streamdb.online/api/health`

#### 3. Deployment Fails
**Cause**: Various deployment script issues
**Solution**:
- Check deployment logs in the admin panel
- Verify deploy script permissions: `chmod +x deployment/deploy.sh`
- Ensure Git repository is accessible
- Check database connectivity

#### 4. Rate Limit Exceeded
**Cause**: Too many webhook requests or deployments
**Solution**:
- Wait for the rate limit window to reset
- Check for webhook loops or excessive pushes
- Use manual deployment sparingly

### Debug Steps

1. **Check webhook delivery in GitHub**:
   - Go to Settings → Webhooks
   - Click on your webhook
   - Check "Recent Deliveries" tab
   - Look for error messages or response codes

2. **Check server logs**:
   ```bash
   pm2 logs streamdb-online
   ```

3. **Test webhook endpoint manually**:
   ```bash
   curl -X GET https://streamdb.online/api/webhook/test
   ```

4. **Check database connectivity**:
   ```bash
   cd /var/www/streamdb_onl_usr/data/www/streamdb.online/server
   node scripts/init-deployment-db.js
   ```

## 📈 Expected Workflow

After successful setup, your deployment workflow will be:

1. **Developer pushes code** to `main` branch
2. **GitHub sends webhook** to `https://streamdb.online/api/webhook/github`
3. **Server validates** signature and payload
4. **Deployment starts** automatically:
   - Pulls latest code from GitHub
   - Installs/updates dependencies
   - Builds the frontend application
   - Restarts the server
   - Updates database logs
5. **Admin panel shows** deployment status and logs
6. **Website reflects** the new changes

## 🎉 Success Indicators

You'll know the webhook is working correctly when:
- ✅ GitHub shows green checkmarks in "Recent Deliveries"
- ✅ Admin panel shows successful deployments
- ✅ Website updates automatically after pushes
- ✅ No error messages in server logs
- ✅ Deployment statistics show successful deployments

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Review server logs: `pm2 logs streamdb-online`
3. Test individual components using the admin panel
4. Verify all prerequisites are met
5. Check GitHub webhook delivery logs

The webhook system is designed to be robust and secure, with comprehensive logging to help diagnose any issues that may arise.
