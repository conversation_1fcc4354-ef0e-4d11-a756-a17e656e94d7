# 🚀 StreamDB Online - Deployment Checklist

## ✅ **Pre-Deployment Checklist**

### **1. Code Quality & Build**
- [x] All console errors fixed
- [x] Frontend builds successfully (`npm run build`)
- [x] No TypeScript/JavaScript errors
- [x] All components have proper error handling
- [x] API service has fallback mechanisms
- [x] Mock data system implemented for testing

### **2. Environment Configuration**
- [x] `.env` file properly configured
- [x] Database credentials set
- [x] JWT secrets generated
- [x] Webhook secret configured
- [x] CORS settings correct
- [x] Production URLs set

### **3. Security**
- [x] No sensitive data in frontend code
- [x] Webhook signature verification enabled
- [x] Rate limiting configured
- [x] IP whitelisting for webhooks
- [x] Admin authentication required
- [x] Database credentials secured

## 🔧 **Server Setup Checklist**

### **1. Prerequisites on Alexhost VPS**
- [ ] Node.js installed (v16+)
- [ ] npm installed
- [ ] PM2 installed globally (`npm install -g pm2`)
- [ ] Git configured
- [ ] Apache/Nginx configured
- [ ] MySQL running and accessible

### **2. Project Deployment**
```bash
# 1. Navigate to project directory
cd /var/www/streamdb_onl_usr/data/www/streamdb.online

# 2. Clone/update repository
git clone https://github.com/aakash171088/Streaming_DB.git .
# OR
git pull origin New-Main-1

# 3. Install dependencies
npm install
cd server && npm install && cd ..

# 4. Build frontend
npm run build

# 5. Set up environment
cp .env.example .env
# Edit .env with production values

# 6. Set permissions
chmod +x deployment/deploy.sh
chmod +x setup-auto-deployment.sh

# 7. Run setup script
./setup-auto-deployment.sh
```

### **3. Apache Configuration**
Add to your Apache virtual host:
```apache
# Proxy API requests to Node.js
ProxyPass /api/ http://localhost:3001/api/
ProxyPassReverse /api/ http://localhost:3001/api/

# Proxy webhook requests to webhook server
ProxyPass /api/webhook/ http://localhost:9000/api/webhook/
ProxyPassReverse /api/webhook/ http://localhost:9000/api/webhook/

ProxyPreserveHost On
```

Then reload Apache:
```bash
sudo systemctl reload apache2
```

### **4. PM2 Process Management**
```bash
# Start main application
pm2 start server/index.js --name "streamdb-online" --env production

# Start webhook handler
pm2 start server/services/webhook-server.js --name "webhook-handler" --env production

# Save PM2 configuration
pm2 save
pm2 startup

# Check status
pm2 list
pm2 logs
```

## 🔗 **GitHub Webhook Setup**

### **1. Repository Webhook Configuration**
1. Go to: https://github.com/aakash171088/Streaming_DB/settings/hooks
2. Click "Add webhook"
3. Configure:
   ```
   Payload URL: https://streamdb.online/api/webhook/github
   Content type: application/json
   Secret: 249fd45813bc97e48c46d634418b77a3c3388d219b9ffcfa9e7edaaa7c28a0ef
   Events: Just the push event
   Active: ✅ Yes
   ```
4. Click "Add webhook"

### **2. Test Webhook**
```bash
# Test webhook connectivity
node test-webhook.js

# Or test manually
curl -X GET https://streamdb.online/api/webhook/test
```

## 🧪 **Testing Checklist**

### **1. Local Testing**
- [x] `npm run build` completes successfully
- [x] `npm run preview` works with mock data
- [x] No console errors in browser
- [x] All pages load correctly
- [x] Navigation works properly
- [x] Loading states display correctly
- [x] Error states work when API unavailable

### **2. Production Testing**
- [ ] Website loads: https://streamdb.online
- [ ] Admin panel accessible: https://streamdb.online/admin
- [ ] API health check: https://streamdb.online/api/health
- [ ] Webhook test: https://streamdb.online/api/webhook/test
- [ ] Database connection working
- [ ] PM2 processes running

### **3. Deployment Testing**
- [ ] Make a test commit to New-Main-1 branch
- [ ] Push to GitHub
- [ ] Verify webhook delivery in GitHub
- [ ] Check deployment logs
- [ ] Verify website updates automatically
- [ ] Test rollback functionality

## 📊 **Monitoring & Maintenance**

### **1. Log Monitoring**
```bash
# Deployment logs
tail -f /var/log/streamdb-deploy.log

# PM2 logs
pm2 logs streamdb-online
pm2 logs webhook-handler

# Apache logs
tail -f /var/log/apache2/error.log
tail -f /var/log/apache2/access.log
```

### **2. Health Checks**
```bash
# Check PM2 processes
pm2 list

# Check system resources
pm2 monit

# Test endpoints
curl https://streamdb.online/api/health
curl https://streamdb.online/api/webhook/test
```

### **3. Database Monitoring**
```bash
# Check database connection
mysql -u dbadmin_streamdb -p streamdb_database

# Check deployment logs in database
SELECT * FROM deployment_logs ORDER BY created_at DESC LIMIT 10;
```

## 🔄 **Deployment Workflow**

### **Automatic Deployment Process:**
1. **Developer pushes** to New-Main-1 branch
2. **GitHub sends webhook** to https://streamdb.online/api/webhook/github
3. **Security validation** (signature, rate limiting, IP check)
4. **Payload validation** (branch, repository verification)
5. **Backup creation** of current version
6. **Code update** via `git pull`
7. **Dependencies installation** (`npm install`)
8. **Frontend build** (`npm run build`)
9. **Service restart** with PM2 (zero downtime)
10. **Logging** to database and files
11. **Success notification** or error reporting

### **Manual Deployment:**
```bash
# SSH to server
ssh streamdb_onl_usr@***********

# Navigate to project
cd /var/www/streamdb_onl_usr/data/www/streamdb.online

# Run deployment
./deployment/deploy.sh deploy

# Check status
./deployment/deploy.sh status

# Rollback if needed
./deployment/deploy.sh rollback
```

## 🚨 **Troubleshooting**

### **Common Issues:**

1. **Webhook not receiving requests:**
   - Check Apache proxy configuration
   - Verify PM2 webhook-handler is running
   - Check firewall settings

2. **Deployment fails:**
   - Check deployment logs
   - Verify git repository access
   - Check file permissions
   - Ensure dependencies can be installed

3. **Website not updating:**
   - Check if build completed successfully
   - Verify Apache is serving from dist/ directory
   - Check for caching issues

4. **Database connection issues:**
   - Verify MySQL is running
   - Check database credentials
   - Test socket connection

## ✅ **Final Verification**

After completing all steps:

1. **Website loads correctly:** https://streamdb.online
2. **Admin panel works:** https://streamdb.online/admin
3. **API responds:** https://streamdb.online/api/health
4. **Webhook configured:** GitHub webhook shows green checkmark
5. **Auto-deployment works:** Test commit triggers deployment
6. **Monitoring active:** Logs are being generated
7. **Backup system working:** Previous versions are backed up

## 🎉 **Success Criteria**

Your GitHub Auto-Deployment is fully functional when:

- ✅ **Zero-downtime deployments** work automatically
- ✅ **Security validation** prevents unauthorized deployments
- ✅ **Complete logging** provides full audit trail
- ✅ **Backup system** allows quick rollbacks
- ✅ **Admin integration** provides deployment management
- ✅ **Rate limiting** prevents deployment spam
- ✅ **Error handling** gracefully manages failures

**🚀 Your StreamDB Online auto-deployment system is now enterprise-ready!**
