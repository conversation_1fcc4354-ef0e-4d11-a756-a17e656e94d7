#!/bin/bash

# Quick fix for Rollup dependency issue on Linux server
echo "🔧 Fixing Rollup dependency issue..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    log_error "package.json not found. Please run this script from the project root directory."
    exit 1
fi

log_info "Step 1: Cleaning npm cache and dependencies..."
rm -rf node_modules package-lock.json
npm cache clean --force
log_success "Cache and dependencies cleaned"

log_info "Step 2: Installing dependencies with platform-specific fixes..."
# Install dependencies with specific flags for Linux
npm install --platform=linux --arch=x64
if [ $? -ne 0 ]; then
    log_warning "Standard install failed, trying alternative approach..."
    npm install --force
fi

log_info "Step 3: Installing Rollup Linux dependencies manually..."
npm install @rollup/rollup-linux-x64-gnu --save-dev --force
npm install rollup --save-dev --force

log_info "Step 4: Verifying installation..."
if [ -d "node_modules/@rollup/rollup-linux-x64-gnu" ]; then
    log_success "Rollup Linux dependencies installed successfully"
else
    log_warning "Rollup Linux dependencies not found, trying alternative..."
    npm install @rollup/rollup-linux-x64-gnu@latest --save-dev --force
fi

log_info "Step 5: Testing build..."
npm run build
if [ $? -eq 0 ]; then
    log_success "Build completed successfully!"
    echo ""
    echo "🎉 Rollup issue fixed! You can now run the main deployment script:"
    echo "   ./deploy-webhook-system.sh"
else
    log_error "Build still failing. Let's try a different approach..."
    
    log_info "Step 6: Alternative fix - using yarn instead of npm..."
    if command -v yarn &> /dev/null; then
        log_info "Yarn found, trying yarn install..."
        yarn install
        yarn build
        if [ $? -eq 0 ]; then
            log_success "Build successful with yarn!"
        else
            log_error "Build failed with yarn as well"
        fi
    else
        log_info "Installing yarn..."
        npm install -g yarn
        yarn install
        yarn build
    fi
fi

echo ""
log_info "If the build is still failing, you can:"
echo "1. Use the pre-built dist folder (if available)"
echo "2. Build locally and upload the dist folder"
echo "3. Skip the build step and run the webhook setup only"
