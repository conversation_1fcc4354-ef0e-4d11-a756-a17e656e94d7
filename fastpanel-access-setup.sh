#!/bin/bash

# FastPanel Access Setup Script
# This script will configure proper FastPanel access

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

# Function to create FastPanel admin user
create_fastpanel_admin() {
    log_info "🔑 Creating FastPanel Admin User"
    echo "================================="
    
    # Create admin user with proper credentials
    /usr/local/fastpanel2/fastpanel users create --username admin --password "StreamDB2025!" 2>/dev/null || log_info "Admin user may already exist"
    
    # Generate login token
    local token=$(/usr/local/fastpanel2/fastpanel usrlogin 2>/dev/null || echo "")
    
    if [ -n "$token" ]; then
        log_success "FastPanel login token generated"
        log_info "Token: $token"
    else
        log_warning "Could not generate login token"
    fi
    
    log_success "FastPanel admin user configured"
    log_info "Username: admin"
    log_info "Password: StreamDB2025!"
}

# Function to test FastPanel web interface
test_fastpanel_web() {
    log_info "🧪 Testing FastPanel Web Interface"
    echo "=================================="
    
    # Test different potential paths
    local paths=(
        "/"
        "/login"
        "/admin"
        "/panel"
        "/fastpanel"
        "/web"
        "/ui"
        "/dashboard"
    )
    
    for path in "${paths[@]}"; do
        local response=$(curl -s -o /dev/null -w "%{http_code}" http://127.0.0.1:5501$path 2>/dev/null)
        log_info "Testing $path: HTTP $response"
        
        if [ "$response" != "404" ]; then
            log_success "Found working path: $path (HTTP $response)"
        fi
    done
}

# Function to check FastPanel configuration
check_fastpanel_config() {
    log_info "🔍 Checking FastPanel Configuration"
    echo "==================================="
    
    # Check if FastPanel web files exist
    if [ -d "/usr/local/fastpanel2/web/public" ]; then
        log_success "FastPanel web files found"
        log_info "Web directory contents:"
        ls -la /usr/local/fastpanel2/web/public/ | head -5
    else
        log_warning "FastPanel web files not found"
    fi
    
    # Check FastPanel configuration
    if [ -f "/usr/local/fastpanel2/app/config/parameters.yml" ]; then
        log_success "FastPanel configuration found"
        log_info "Current configuration:"
        grep -E "(host|port|web_root)" /usr/local/fastpanel2/app/config/parameters.yml || log_info "No web configuration found"
    fi
    
    # Check FastPanel processes
    log_info "FastPanel processes:"
    ps aux | grep fastpanel | grep -v grep || log_warning "No FastPanel processes found"
}

# Function to configure FastPanel web access
configure_fastpanel_web() {
    log_info "🔧 Configuring FastPanel Web Access"
    echo "===================================="
    
    # Update FastPanel configuration for web access
    local config_file="/usr/local/fastpanel2/app/config/parameters.yml"
    
    # Backup current config
    cp "$config_file" "$config_file.backup.$(date +%Y%m%d_%H%M%S)"
    
    # Create updated configuration
    cat > "$config_file" << 'EOF'
parameters:
  database_name: ""
  database_user: ""
  database_password: ""
  database_host: ""
  sqlite:
    main: /usr/local/fastpanel2/app/db/fastpanel2.db
  host: "127.0.0.1"
  port: 5501
  web_root: "/usr/local/fastpanel2/web/public"
  web_enabled: true
  debug: false
  auth_required: true
EOF
    
    log_success "FastPanel configuration updated"
    
    # Set proper permissions
    chown root:root "$config_file"
    chmod 600 "$config_file"
    
    # Restart FastPanel to apply changes
    log_info "Restarting FastPanel to apply changes..."
    pkill -f fastpanel 2>/dev/null || true
    sleep 3
    
    /usr/local/fastpanel2/fastpanel start &
    sleep 5
    
    if netstat -tlnp | grep -q ":5501"; then
        log_success "FastPanel restarted successfully"
    else
        log_error "FastPanel failed to restart"
        return 1
    fi
}

# Function to create FastPanel access instructions
create_access_instructions() {
    log_info "📋 Creating FastPanel Access Instructions"
    echo "========================================="
    
    cat > /root/fastpanel-access-guide.txt << 'EOF'
# FastPanel Access Guide for StreamDB.online

## Access URLs:
- External: https://fastpanel.streamdb.online/
- Internal: http://127.0.0.1:5501/
- Proxy: http://45.93.8.197:5502/

## Credentials:
- Username: admin
- Password: StreamDB2025!

## Troubleshooting:
1. If you get 404 errors, FastPanel may be running but not serving the web interface
2. Try accessing different paths: /login, /admin, /panel
3. Check if FastPanel web files exist in /usr/local/fastpanel2/web/public/
4. Verify FastPanel is running: ps aux | grep fastpanel

## Service Management:
- Start: systemctl start fastpanel-streamdb
- Stop: systemctl stop fastpanel-streamdb
- Status: systemctl status fastpanel-streamdb
- Logs: journalctl -u fastpanel-streamdb -f

## Manual Commands:
- Start FastPanel: /usr/local/fastpanel2/fastpanel start
- Create user: /usr/local/fastpanel2/fastpanel users create --username admin --password "StreamDB2025!"
- Generate token: /usr/local/fastpanel2/fastpanel usrlogin

## Health Check:
Run: /usr/local/bin/streamdb-health-check.sh
EOF
    
    log_success "Access guide created: /root/fastpanel-access-guide.txt"
}

# Function to test external access
test_external_access() {
    log_info "🌐 Testing External Access"
    echo "=========================="
    
    # Test proxy access
    local proxy_response=$(curl -s -o /dev/null -w "%{http_code}" http://45.93.8.197:5502/ 2>/dev/null)
    log_info "Proxy access (45.93.8.197:5502): HTTP $proxy_response"
    
    # Test if socat is working
    if netstat -tlnp | grep -q ":5502"; then
        log_success "Socat proxy is running"
    else
        log_error "Socat proxy is not running"
    fi
    
    # Test FastPanel direct access
    local direct_response=$(curl -s -o /dev/null -w "%{http_code}" http://127.0.0.1:5501/ 2>/dev/null)
    log_info "Direct FastPanel access: HTTP $direct_response"
}

# Main execution
main() {
    log_info "🔧 FastPanel Access Setup"
    log_info "========================="
    
    check_fastpanel_config
    echo ""
    create_fastpanel_admin
    echo ""
    test_fastpanel_web
    echo ""
    configure_fastpanel_web
    echo ""
    test_external_access
    echo ""
    create_access_instructions
    
    echo ""
    log_success "🎉 FastPanel Access Setup Completed!"
    log_info ""
    log_info "📋 Summary:"
    log_info "   ✅ FastPanel admin user: admin / StreamDB2025!"
    log_info "   ✅ Configuration updated"
    log_info "   ✅ Service restarted"
    log_info "   ✅ Access guide created"
    
    echo ""
    log_info "🌐 Try accessing FastPanel at:"
    log_info "   https://fastpanel.streamdb.online/"
    log_info "   http://45.93.8.197:5502/"
    
    echo ""
    log_info "📖 For detailed instructions, see:"
    log_info "   /root/fastpanel-access-guide.txt"
}

# Run main function
main "$@"
