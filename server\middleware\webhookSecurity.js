const crypto = require('crypto');
const rateLimit = require('express-rate-limit');
const db = require('../config/database');

// Configuration
const WEBHOOK_SECRET = process.env.WEBHOOK_SECRET;
const MAX_PAYLOAD_SIZE = 1024 * 1024; // 1MB
const ALLOWED_IPS = [
  '************/20',    // GitHub webhook IPs
  '*************/22',   // GitHub webhook IPs
  '************/22',    // GitHub webhook IPs
  '127.0.0.1',          // Localhost for testing
  '::1'                 // IPv6 localhost
];

// Rate limiting for webhook endpoints
const webhookRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 50, // Limit each IP to 50 requests per windowMs
  message: {
    error: 'Too many webhook requests',
    message: 'Rate limit exceeded. Please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false,
  skip: (req) => {
    // Skip rate limiting for admin panel requests
    return req.headers['user-agent']?.includes('Mozilla') || 
           req.headers.referer?.includes('/admin');
  }
});

// Deployment rate limiting (stricter)
const deploymentRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10, // Maximum 10 deployments per hour
  message: {
    error: 'Deployment rate limit exceeded',
    message: 'Too many deployments in the last hour. Please wait before triggering another deployment.'
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    // Use a combination of IP and user agent for key generation
    return `${req.ip}-${crypto.createHash('md5').update(req.headers['user-agent'] || '').digest('hex')}`;
  }
});

// IP whitelist middleware
function ipWhitelist(req, res, next) {
  const clientIP = req.headers['x-forwarded-for'] || 
                   req.headers['x-real-ip'] || 
                   req.connection.remoteAddress || 
                   req.socket.remoteAddress ||
                   (req.connection.socket ? req.connection.socket.remoteAddress : null);

  // Log the request for monitoring
  logSecurityEvent('IP_CHECK', {
    ip: clientIP,
    userAgent: req.headers['user-agent'],
    path: req.path,
    method: req.method
  });

  // Allow admin panel requests (they go through authentication)
  if (req.path.includes('/admin') || req.headers.referer?.includes('/admin')) {
    return next();
  }

  // For webhook requests, check if IP is from GitHub
  if (req.path.includes('/webhook/github')) {
    // In production, you might want to verify GitHub IPs more strictly
    // For now, we'll rely on signature verification as the primary security measure
    return next();
  }

  next();
}

// Signature verification middleware
function verifyWebhookSignature(req, res, next) {
  const signature = req.headers['x-hub-signature-256'];
  const payload = req.body;

  if (!WEBHOOK_SECRET) {
    logSecurityEvent('SECURITY_WARNING', {
      message: 'Webhook secret not configured',
      ip: req.ip
    });
    return res.status(500).json({
      error: 'Server Configuration Error',
      message: 'Webhook secret not configured'
    });
  }

  if (!signature) {
    logSecurityEvent('SIGNATURE_MISSING', {
      ip: req.ip,
      userAgent: req.headers['user-agent']
    });
    return res.status(401).json({
      error: 'Unauthorized',
      message: 'Missing signature header'
    });
  }

  // Verify signature
  const expectedSignature = 'sha256=' + crypto
    .createHmac('sha256', WEBHOOK_SECRET)
    .update(payload)
    .digest('hex');

  const isValid = crypto.timingSafeEqual(
    Buffer.from(signature),
    Buffer.from(expectedSignature)
  );

  if (!isValid) {
    logSecurityEvent('SIGNATURE_INVALID', {
      ip: req.ip,
      userAgent: req.headers['user-agent'],
      providedSignature: signature.substring(0, 20) + '...',
      expectedSignature: expectedSignature.substring(0, 20) + '...'
    });
    return res.status(401).json({
      error: 'Unauthorized',
      message: 'Invalid signature'
    });
  }

  logSecurityEvent('SIGNATURE_VALID', {
    ip: req.ip,
    userAgent: req.headers['user-agent']
  });

  next();
}

// Payload validation middleware
function validateWebhookPayload(req, res, next) {
  const payload = req.body;

  // Check payload size
  if (payload.length > MAX_PAYLOAD_SIZE) {
    logSecurityEvent('PAYLOAD_TOO_LARGE', {
      ip: req.ip,
      size: payload.length
    });
    return res.status(413).json({
      error: 'Payload Too Large',
      message: 'Webhook payload exceeds maximum size limit'
    });
  }

  // Parse and validate JSON
  let data;
  try {
    data = JSON.parse(payload.toString());
  } catch (error) {
    logSecurityEvent('INVALID_JSON', {
      ip: req.ip,
      error: error.message
    });
    return res.status(400).json({
      error: 'Bad Request',
      message: 'Invalid JSON payload'
    });
  }

  // Validate GitHub webhook structure
  if (!data.repository || !data.repository.full_name) {
    logSecurityEvent('INVALID_WEBHOOK_STRUCTURE', {
      ip: req.ip,
      hasRepository: !!data.repository
    });
    return res.status(400).json({
      error: 'Bad Request',
      message: 'Invalid webhook payload structure'
    });
  }

  // Check if it's from the expected repository
  const expectedRepo = process.env.GITHUB_REPO || 'aakash171088/Streaming_DB';
  if (data.repository.full_name !== expectedRepo) {
    logSecurityEvent('UNAUTHORIZED_REPOSITORY', {
      ip: req.ip,
      repository: data.repository.full_name,
      expected: expectedRepo
    });
    return res.status(403).json({
      error: 'Forbidden',
      message: 'Webhook from unauthorized repository'
    });
  }

  // Store parsed data for use in route handlers
  req.webhookData = data;
  next();
}

// Security event logging
function logSecurityEvent(event, data = {}) {
  const logEntry = {
    timestamp: new Date().toISOString(),
    event,
    ...data
  };

  console.log(`[SECURITY] ${event}:`, logEntry);

  // Store in database if available
  if (db && db.query) {
    db.query(
      'INSERT INTO deployment_logs (level, message, data) VALUES (?, ?, ?)',
      ['WARNING', `Security event: ${event}`, JSON.stringify(logEntry)],
      (err) => {
        if (err) console.error('Failed to log security event:', err);
      }
    );
  }
}

// Admin authentication middleware for webhook management endpoints
function requireAdminAuth(req, res, next) {
  // Check if user is authenticated (this should integrate with your existing auth system)
  const authHeader = req.headers.authorization;
  const sessionCookie = req.headers.cookie;

  if (!authHeader && !sessionCookie) {
    return res.status(401).json({
      error: 'Unauthorized',
      message: 'Authentication required for webhook management'
    });
  }

  // For now, we'll assume the main auth middleware handles this
  // In a real implementation, you'd verify the JWT token or session
  next();
}

// Comprehensive security middleware stack for webhooks
const webhookSecurityStack = [
  webhookRateLimit,
  ipWhitelist,
  verifyWebhookSignature,
  validateWebhookPayload
];

// Security middleware stack for admin webhook endpoints
const adminWebhookSecurityStack = [
  webhookRateLimit,
  requireAdminAuth
];

// Deployment security middleware (for manual deployments)
const deploymentSecurityStack = [
  deploymentRateLimit,
  requireAdminAuth
];

module.exports = {
  webhookSecurityStack,
  adminWebhookSecurityStack,
  deploymentSecurityStack,
  verifyWebhookSignature,
  validateWebhookPayload,
  ipWhitelist,
  logSecurityEvent,
  webhookRateLimit,
  deploymentRateLimit
};
