#!/bin/bash

# StreamDB Production Fix Script
# This script fixes the module loading and favicon issues

echo "🔧 StreamDB Production Fix Script"
echo "================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    log_error "package.json not found. Please run this script from the project root directory."
    exit 1
fi

log_info "Step 1: Checking current environment..."
echo "Current directory: $(pwd)"
echo "Node.js version: $(node --version)"
echo "NPM version: $(npm --version)"

# Check if dist directory exists
if [ ! -d "dist" ]; then
    log_warning "Dist directory not found. Building application..."
    npm run build
    if [ $? -ne 0 ]; then
        log_error "Build failed. Please check for errors."
        exit 1
    fi
    log_success "Application built successfully"
else
    log_info "Dist directory exists. Checking if rebuild is needed..."
    
    # Check if dist/index.html contains built assets
    if grep -q "/assets/" dist/index.html; then
        log_success "Built assets found in dist/index.html"
    else
        log_warning "Dist appears to contain development files. Rebuilding..."
        npm run build
        if [ $? -ne 0 ]; then
            log_error "Build failed. Please check for errors."
            exit 1
        fi
        log_success "Application rebuilt successfully"
    fi
fi

log_info "Step 2: Stopping existing server processes..."
# Stop PM2 processes if they exist
if command -v pm2 &> /dev/null; then
    pm2 stop streamdb-online 2>/dev/null || log_info "No existing PM2 process to stop"
    pm2 delete streamdb-online 2>/dev/null || log_info "No existing PM2 process to delete"
else
    log_warning "PM2 not found. Please install PM2: npm install -g pm2"
fi

log_info "Step 3: Installing/updating server dependencies..."
cd server
npm install --production
if [ $? -ne 0 ]; then
    log_error "Failed to install server dependencies"
    exit 1
fi
cd ..

log_info "Step 4: Starting production server..."
cd server

# Set environment variables and start with PM2
export NODE_ENV=production
export PORT=3001

if command -v pm2 &> /dev/null; then
    pm2 start index.js --name streamdb-online --env production
    if [ $? -eq 0 ]; then
        log_success "Server started successfully with PM2"
        pm2 save
        log_success "PM2 configuration saved"
    else
        log_error "Failed to start server with PM2"
        exit 1
    fi
else
    log_warning "PM2 not available. Starting with Node.js directly..."
    nohup node index.js > ../server.log 2>&1 &
    log_success "Server started in background"
fi

cd ..

log_info "Step 5: Checking server status..."
sleep 3

# Test if server is responding
if command -v curl &> /dev/null; then
    if curl -f http://localhost:3001/api/health > /dev/null 2>&1; then
        log_success "Server is responding to health checks"
    else
        log_warning "Server health check failed. Check logs for details."
    fi
else
    log_info "curl not available. Please test manually: http://localhost:3001/api/health"
fi

log_success "Production fix completed!"
echo ""
echo "🌐 Your website should now be accessible"
echo "📋 Next steps:"
echo "   1. Test the website in your browser"
echo "   2. Check that favicon files load correctly"
echo "   3. Verify that the React app loads without module errors"
echo "   4. Test admin panel functionality"
echo ""
echo "📊 To check server status: pm2 status"
echo "📋 To view logs: pm2 logs streamdb-online"
echo "🔄 To restart: pm2 restart streamdb-online"
